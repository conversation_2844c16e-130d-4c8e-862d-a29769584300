package com.mycompany.giatui.app;

import view.MainFrame;
import util.DBConnection;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.JOptionPane;
import java.sql.Connection;

/**
 * Main class của ứng dụng Quản Lý Giặt Ủi
 * <AUTHOR>
 */
public class GiatUiAPP {
    
    public static void main(String[] args) {
        // Thiết lập Look and Feel
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            System.err.println("Không thể thiết lập Look and Feel: " + e.getMessage());
        }
        
        // Kiểm tra kết nối database trước khi khởi động ứng dụng
        SwingUtilities.invokeLater(() -> {
            if (kiemTraKetNoiDatabase()) {
                // Khởi động ứng dụng chính
                new MainFrame().setVisible(true);
                System.out.println("Ứng dụng Quản Lý Giặt Ủi đã khởi động thành công!");
            } else {
                // Hiển thị thông báo lỗi và thoát
                JOptionPane.showMessageDialog(null, 
                    "Không thể kết nối đến database!\n" +
                    "Vui lòng kiểm tra:\n" +
                    "1. SQL Server đang chạy\n" +
                    "2. Database 'QuanLyGiatUi' đã được tạo\n" +
                    "3. Thông tin kết nối trong DBConnection.java\n\n" +
                    "Chạy script database/RunDatabase.bat để tạo database.",
                    "Lỗi kết nối Database", 
                    JOptionPane.ERROR_MESSAGE);
                System.exit(1);
            }
        });
    }
    
    /**
     * Kiểm tra kết nối database
     * @return true nếu kết nối thành công, false nếu thất bại
     */
    private static boolean kiemTraKetNoiDatabase() {
        try {
            System.out.println("Đang kiểm tra kết nối database...");
            Connection conn = DBConnection.getConnection();
            
            if (conn != null) {
                System.out.println("✓ Kết nối database thành công!");
                conn.close();
                return true;
            } else {
                System.err.println("✗ Không thể kết nối database!");
                return false;
            }
        } catch (Exception e) {
            System.err.println("✗ Lỗi khi kiểm tra kết nối database: " + e.getMessage());
            return false;
        }
    }
}
