package test;

import util.DBConnection;
import model.*;
import java.sql.Connection;
import java.util.List;

/**
 * Class test kết nối database và các DAO
 * <AUTHOR>
 */
public class TestDatabase {
    
    public static void main(String[] args) {
        System.out.println("=== TEST KẾT NỐI DATABASE ===");
        
        // Test kết nối cơ bản
        testKetNoiDatabase();
        
        // Test các DAO
        testKhachHangDAO();
        testDichVuDAO();
        testDonHangDAO();
        
        System.out.println("\n=== HOÀN THÀNH TEST ===");
    }
    
    private static void testKetNoiDatabase() {
        System.out.println("\n1. Test kết nối database:");
        try {
            Connection conn = DBConnection.getConnection();
            if (conn != null) {
                System.out.println("   ✓ Kết nối database thành công!");
                conn.close();
            } else {
                System.out.println("   ✗ Không thể kết nối database!");
            }
        } catch (Exception e) {
            System.out.println("   ✗ Lỗi kết nối: " + e.getMessage());
        }
    }
    
    private static void testKhachHangDAO() {
        System.out.println("\n2. Test KhachHangDAO:");
        try {
            KhachHangDAO dao = new KhachHangDAO();
            
            // Test lấy danh sách
            List<KhachHang> danhSach = dao.layTatCaKhachHang();
            System.out.println("   ✓ Lấy danh sách khách hàng: " + danhSach.size() + " khách hàng");
            
            // Test thêm khách hàng
            KhachHang kh = new KhachHang("Test User", "**********", "Test Address", "<EMAIL>");
            if (dao.themKhachHang(kh)) {
                System.out.println("   ✓ Thêm khách hàng thành công");
                
                // Test tìm kiếm
                KhachHang khTimThay = dao.layKhachHangTheoSDT("**********");
                if (khTimThay != null) {
                    System.out.println("   ✓ Tìm khách hàng theo SĐT thành công");
                    
                    // Test xóa
                    if (dao.xoaKhachHang(khTimThay.getMaKH())) {
                        System.out.println("   ✓ Xóa khách hàng thành công");
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("   ✗ Lỗi test KhachHangDAO: " + e.getMessage());
        }
    }
    
    private static void testDichVuDAO() {
        System.out.println("\n3. Test DichVuDAO:");
        try {
            DichVuDAO dao = new DichVuDAO();
            
            // Test lấy danh sách
            List<DichVu> danhSach = dao.layTatCaDichVu();
            System.out.println("   ✓ Lấy danh sách dịch vụ: " + danhSach.size() + " dịch vụ");
            
            // Test thêm dịch vụ
            DichVu dv = new DichVu("Test Service", 50000, "kg", "Test description");
            if (dao.themDichVu(dv)) {
                System.out.println("   ✓ Thêm dịch vụ thành công");
                
                // Test tìm kiếm
                DichVu dvTimThay = dao.layDichVuTheoTen("Test Service");
                if (dvTimThay != null) {
                    System.out.println("   ✓ Tìm dịch vụ theo tên thành công");
                    
                    // Test xóa
                    if (dao.xoaDichVu(dvTimThay.getMaDV())) {
                        System.out.println("   ✓ Xóa dịch vụ thành công");
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("   ✗ Lỗi test DichVuDAO: " + e.getMessage());
        }
    }
    
    private static void testDonHangDAO() {
        System.out.println("\n4. Test DonHangDAO:");
        try {
            DonHangDAO dao = new DonHangDAO();
            
            // Test lấy danh sách
            List<DonHang> danhSach = dao.layTatCaDonHang();
            System.out.println("   ✓ Lấy danh sách đơn hàng: " + danhSach.size() + " đơn hàng");
            
            // Test đếm theo trạng thái
            int soDangXuLy = dao.demDonHangTheoTrangThai("Đang xử lý");
            int soDaHoanThanh = dao.demDonHangTheoTrangThai("Đã hoàn thành");
            System.out.println("   ✓ Đơn hàng đang xử lý: " + soDangXuLy);
            System.out.println("   ✓ Đơn hàng đã hoàn thành: " + soDaHoanThanh);
            
        } catch (Exception e) {
            System.out.println("   ✗ Lỗi test DonHangDAO: " + e.getMessage());
        }
    }
}
