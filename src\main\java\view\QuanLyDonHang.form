<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.5" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JPanelFormInfo">
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace min="-2" pref="304" max="-2" attributes="0"/>
              <Component id="jLabel1" min="-2" pref="261" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jPanel1" max="32767" attributes="0"/>
          </Group>
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jPanel2" max="32767" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jPanel3" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" attributes="0">
                      <EmptySpace min="-2" pref="16" max="-2" attributes="0"/>
                      <Component id="jPanel1" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" attributes="0">
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="jLabel1" max="32767" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace min="-2" pref="10" max="-2" attributes="0"/>
              <Component id="jPanel2" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jPanel3" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="jPanel1">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <EmptySpace min="0" pref="32" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
      </Layout>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="24" style="0"/>
        </Property>
        <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="ff" green="0" red="0" type="rgb"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Qu&#x1ea3;n L&#xfd; &#x110;&#x1a1;n H&#xe0;ng"/>
      </Properties>
    </Component>
    <Container class="javax.swing.JPanel" name="jPanel2">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="112" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="102" attributes="0">
                          <Component id="jLabel3" min="-2" max="-2" attributes="0"/>
                          <EmptySpace type="separate" max="-2" attributes="0"/>
                          <Component id="txtngaynhan" max="32767" attributes="0"/>
                      </Group>
                      <Group type="102" alignment="0" attributes="0">
                          <Group type="103" groupAlignment="0" attributes="0">
                              <Component id="jLabel2" min="-2" max="-2" attributes="0"/>
                              <Component id="jLabel4" alignment="0" min="-2" max="-2" attributes="0"/>
                              <Component id="jLabel5" alignment="0" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <Group type="103" groupAlignment="0" attributes="0">
                              <Group type="102" attributes="0">
                                  <EmptySpace min="-2" pref="59" max="-2" attributes="0"/>
                                  <Component id="btncapnhat" min="-2" max="-2" attributes="0"/>
                                  <EmptySpace min="-2" pref="70" max="-2" attributes="0"/>
                                  <Component id="btnxoadon" min="-2" max="-2" attributes="0"/>
                                  <EmptySpace min="-2" pref="87" max="-2" attributes="0"/>
                                  <Component id="btntaodon" min="-2" max="-2" attributes="0"/>
                                  <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
                              </Group>
                              <Group type="102" attributes="0">
                                  <EmptySpace min="-2" pref="36" max="-2" attributes="0"/>
                                  <Component id="txtngaytra" max="32767" attributes="0"/>
                              </Group>
                              <Group type="102" alignment="0" attributes="0">
                                  <EmptySpace min="-2" pref="34" max="-2" attributes="0"/>
                                  <Group type="103" groupAlignment="0" attributes="0">
                                      <Group type="102" attributes="0">
                                          <Component id="cbodichvu" min="-2" pref="204" max="-2" attributes="0"/>
                                          <EmptySpace pref="25" max="32767" attributes="0"/>
                                          <Component id="jLabel6" min="-2" max="-2" attributes="0"/>
                                          <EmptySpace min="-2" pref="33" max="-2" attributes="0"/>
                                          <Component id="cbotrangthaidon" min="-2" pref="200" max="-2" attributes="0"/>
                                      </Group>
                                      <Component id="txthoten" max="32767" attributes="0"/>
                                  </Group>
                              </Group>
                          </Group>
                      </Group>
                  </Group>
                  <EmptySpace min="-2" pref="269" max="-2" attributes="0"/>
              </Group>
              <Group type="102" alignment="0" attributes="0">
                  <Component id="jLabel7" min="-2" pref="159" max="-2" attributes="0"/>
                  <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="1" attributes="0">
                  <EmptySpace min="-2" pref="15" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="jLabel2" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="txtngaytra" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace type="separate" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="jLabel3" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="txtngaynhan" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="24" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="jLabel4" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="txthoten" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="27" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="jLabel5" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="cbodichvu" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                      <Component id="jLabel6" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="cbotrangthaidon" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="28" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="btnxoadon" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                      <Component id="btncapnhat" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                      <Component id="btntaodon" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace pref="41" max="32767" attributes="0"/>
                  <Component id="jLabel7" min="-2" max="-2" attributes="0"/>
                  <EmptySpace max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JButton" name="btntaodon">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="T&#x1ea1;o &#x110;&#x1a1;n "/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btntaodonActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel2">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="H&#x1ecd; T&#xea;n"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="btnxoadon">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Xo&#xe1; &#x110;&#x1a1;n "/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnxoadonActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel3">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Ng&#xe0;y Nh&#x1ead;n"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="btncapnhat">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="C&#x1ead;p Nh&#x1ead;t"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btncapnhatActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel4">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Ng&#xe0;y Tr&#x1ea3;"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="txtngaytra">
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel5">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="D&#x1ecb;ch V&#x1ee5;"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JComboBox" name="cbotrangthaidon">
          <Properties>
            <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
              <StringArray count="4">
                <StringItem index="0" value="Item 1"/>
                <StringItem index="1" value="Item 2"/>
                <StringItem index="2" value="Item 3"/>
                <StringItem index="3" value="Item 4"/>
              </StringArray>
            </Property>
          </Properties>
          <AuxValues>
            <AuxValue name="JavaCodeGenerator_TypeParameters" type="java.lang.String" value="&lt;String&gt;"/>
          </AuxValues>
        </Component>
        <Component class="javax.swing.JTextField" name="txthoten">
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel6">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Tr&#x1ea1;ng Th&#xe1;i &#x110;&#x1a1;n"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JTextField" name="txtngaynhan">
        </Component>
        <Component class="javax.swing.JComboBox" name="cbodichvu">
          <Properties>
            <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
              <StringArray count="4">
                <StringItem index="0" value="Item 1"/>
                <StringItem index="1" value="Item 2"/>
                <StringItem index="2" value="Item 3"/>
                <StringItem index="3" value="Item 4"/>
              </StringArray>
            </Property>
          </Properties>
          <AuxValues>
            <AuxValue name="JavaCodeGenerator_TypeParameters" type="java.lang.String" value="&lt;String&gt;"/>
          </AuxValues>
        </Component>
        <Component class="javax.swing.JLabel" name="jLabel7">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="16" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Th&#xf4;ng Tin &#x110;&#x1a1;n H&#xe0;ng"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPanel" name="jPanel3">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="1" attributes="0">
                  <EmptySpace pref="48" max="32767" attributes="0"/>
                  <Component id="jScrollPane2" min="-2" pref="935" max="-2" attributes="0"/>
                  <EmptySpace max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace max="-2" attributes="0"/>
                  <Component id="jScrollPane2" min="-2" pref="293" max="-2" attributes="0"/>
                  <EmptySpace pref="140" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Container class="javax.swing.JScrollPane" name="jScrollPane2">
          <AuxValues>
            <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
          </AuxValues>

          <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
          <SubComponents>
            <Component class="javax.swing.JTable" name="tblthongtindonhang">
              <Properties>
                <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
                  <Table columnCount="4" rowCount="4">
                    <Column editable="true" title="T&#xea;n Kh&#xe1;ch H&#xe0;ng" type="java.lang.Object"/>
                    <Column editable="true" title="Ng&#xe0;y Nh&#x1ead;n" type="java.lang.Object"/>
                    <Column editable="true" title="Ng&#xe0;y Tr&#x1ea3;" type="java.lang.Object"/>
                    <Column editable="true" title="D&#x1ecb;ch V&#x1ee5;" type="java.lang.Object"/>
                  </Table>
                </Property>
                <Property name="columnModel" type="javax.swing.table.TableColumnModel" editor="org.netbeans.modules.form.editors2.TableColumnModelEditor">
                  <TableColumnModel selectionModel="0">
                    <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                      <Title/>
                      <Editor/>
                      <Renderer/>
                    </Column>
                    <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                      <Title/>
                      <Editor/>
                      <Renderer/>
                    </Column>
                    <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                      <Title/>
                      <Editor/>
                      <Renderer/>
                    </Column>
                    <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                      <Title/>
                      <Editor/>
                      <Renderer/>
                    </Column>
                  </TableColumnModel>
                </Property>
                <Property name="tableHeader" type="javax.swing.table.JTableHeader" editor="org.netbeans.modules.form.editors2.JTableHeaderEditor">
                  <TableHeader reorderingAllowed="true" resizingAllowed="true"/>
                </Property>
              </Properties>
            </Component>
          </SubComponents>
        </Container>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
