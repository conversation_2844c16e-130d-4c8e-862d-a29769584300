-- <PERSON><PERSON><PERSON> chèn dữ liệu mẫu cho hệ thống Quản Lý Giặt Ủi
USE QuanLyGiatUi;
GO

-- <PERSON><PERSON><PERSON> dữ liệu mẫu cho bảng <PERSON>
INSERT INTO KhachHang (TenKhachHang, SoDien<PERSON>, DiaChi, Email) VALUES
(N'Nguyễn <PERSON>ăn <PERSON>', '0901234567', N'123 Đường ABC, Quận 1, TP.HCM', 'nguy<PERSON><PERSON><EMAIL>'),
(N'Trần Thị Bình', '0912345678', N'456 Đường DEF, Quận 2, TP.HCM', '<EMAIL>'),
(N'Lê Văn <PERSON>', '0923456789', N'789 Đường GHI, Quận 3, TP.HCM', 'levan<PERSON><PERSON>@email.com'),
(N'Phạm Thị Dung', '0934567890', N'321 Đường <PERSON>, Quậ<PERSON> 4, TP.HCM', '<EMAIL>'),
(N'Hoàng Văn Em', '0945678901', N'654 Đường MNO, Quận 5, T<PERSON>.HCM', '<EMAIL>');

-- Chèn dữ liệu mẫu cho bảng DichVu
INSERT INTO DichVu (TenDichVu, DonGia, DonVi, MoTa) VALUES
(N'Giặt ủi thường', 25000, N'kg', N'Giặt và ủi quần áo thông thường'),
(N'Giặt hấp', 35000, N'kg', N'Giặt hấp cho quần áo cao cấp'),
(N'Ủi đồ', 15000, N'kg', N'Chỉ ủi không giặt'),
(N'Gấp đồ', 10000, N'kg', N'Gấp đồ sau khi giặt ủi'),
(N'Giặt nhanh', 40000, N'kg', N'Giặt ủi trong ngày'),
(N'Giặt khô', 50000, N'bộ', N'Giặt khô cho vest, áo khoác'),
(N'Giặt chăn màn', 60000, N'cái', N'Giặt chăn ga gối đệm');

-- Chèn dữ liệu mẫu cho bảng DonHang
INSERT INTO DonHang (MaKH, NgayNhan, NgayHenTra, TrangThai, GhiChu, NguoiTao) VALUES
(1, '2024-01-15 08:30:00', '2024-01-17 17:00:00', N'Đã hoàn thành', N'Khách hàng VIP', N'admin'),
(2, '2024-01-16 09:15:00', '2024-01-18 16:00:00', N'Đang xử lý', N'Giao hàng tận nơi', N'admin'),
(3, '2024-01-17 10:00:00', '2024-01-19 15:30:00', N'Đang xử lý', N'', N'admin'),
(1, '2024-01-18 14:20:00', '2024-01-20 17:00:00', N'Đã hoàn thành', N'Đơn hàng thứ 2', N'admin'),
(4, '2024-01-19 11:45:00', '2024-01-21 16:00:00', N'Đang xử lý', N'Khách hàng mới', N'admin');

-- Cập nhật NgayTra cho các đơn đã hoàn thành
UPDATE DonHang SET NgayTra = '2024-01-17 16:30:00' WHERE MaDon = 1;
UPDATE DonHang SET NgayTra = '2024-01-20 16:45:00' WHERE MaDon = 4;

-- Chèn dữ liệu mẫu cho bảng ChiTietDonHang
INSERT INTO ChiTietDonHang (MaDon, MaDV, SoLuong, DonGia, ThanhTien, GhiChu) VALUES
-- Đơn hàng 1
(1, 1, 3.5, 25000, 87500, N'Quần áo thường ngày'),
(1, 3, 2.0, 15000, 30000, N'Ủi thêm áo sơ mi'),
-- Đơn hàng 2  
(2, 2, 2.5, 35000, 87500, N'Áo vest cao cấp'),
(2, 4, 2.5, 10000, 25000, N'Gấp đồ cẩn thận'),
-- Đơn hàng 3
(3, 5, 1.5, 40000, 60000, N'Cần gấp trong ngày'),
-- Đơn hàng 4
(4, 1, 4.0, 25000, 100000, N'Quần áo gia đình'),
(4, 7, 1.0, 60000, 60000, N'Chăn ga gối đệm'),
-- Đơn hàng 5
(5, 6, 2.0, 50000, 100000, N'Vest công sở');

-- Chèn dữ liệu mẫu cho bảng HoaDon
INSERT INTO HoaDon (MaDon, TongTien, ThanhToan, PhuongThucTT, TrangThai, NguoiLap) VALUES
(1, 117500, 117500, N'Tiền mặt', N'Đã thanh toán', N'admin'),
(2, 112500, 50000, N'Chuyển khoản', N'Thanh toán một phần', N'admin'),
(3, 60000, 0, N'Tiền mặt', N'Chưa thanh toán', N'admin'),
(4, 160000, 160000, N'Thẻ', N'Đã thanh toán', N'admin'),
(5, 100000, 0, N'Tiền mặt', N'Chưa thanh toán', N'admin');

-- Tạo view để xem thông tin đơn hàng chi tiết
CREATE VIEW vw_DonHangChiTiet AS
SELECT 
    dh.MaDon,
    kh.TenKhachHang,
    kh.SoDienThoai,
    dh.NgayNhan,
    dh.NgayHenTra,
    dh.NgayTra,
    dh.TongTien,
    dh.TrangThai,
    dh.GhiChu,
    STRING_AGG(dv.TenDichVu + ' (' + CAST(ct.SoLuong AS VARCHAR) + ' ' + dv.DonVi + ')', ', ') AS DichVu
FROM DonHang dh
JOIN KhachHang kh ON dh.MaKH = kh.MaKH
JOIN ChiTietDonHang ct ON dh.MaDon = ct.MaDon
JOIN DichVu dv ON ct.MaDV = dv.MaDV
GROUP BY dh.MaDon, kh.TenKhachHang, kh.SoDienThoai, dh.NgayNhan, 
         dh.NgayHenTra, dh.NgayTra, dh.TongTien, dh.TrangThai, dh.GhiChu;
GO

-- Tạo view thống kê doanh thu
CREATE VIEW vw_ThongKeDoanhThu AS
SELECT 
    CAST(hd.NgayLap AS DATE) AS Ngay,
    COUNT(hd.MaHD) AS SoHoaDon,
    SUM(hd.TongTien) AS TongDoanhThu,
    SUM(hd.ThanhToan) AS DaThanhToan,
    SUM(hd.ConLai) AS ConLai
FROM HoaDon hd
GROUP BY CAST(hd.NgayLap AS DATE);
GO
