package model;

import util.DBConnection;
import java.sql.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * DAO class cho Hóa đơn
 * <AUTHOR>
 */
public class HoaDonDAO {
    
    // Thêm hóa đơn mới
    public int themHoaDon(HoaDon hoaDon) {
        String sql = "INSERT INTO HoaDon (MaDon, TongTien, ThanhToan, PhuongThucTT, NguoiLap) " +
                    "VALUES (?, ?, ?, ?, ?)";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setInt(1, hoaDon.getMaDon());
            stmt.setBigDecimal(2, hoaDon.getTongTien());
            stmt.setBigDecimal(3, hoaDon.getThanhToan());
            stmt.setString(4, hoaDon.getPhuongThucTT());
            stmt.setString(5, hoaDon.getNguoiLap());
            
            int result = stmt.executeUpdate();
            if (result > 0) {
                ResultSet rs = stmt.getGeneratedKeys();
                if (rs.next()) {
                    return rs.getInt(1); // Trả về mã hóa đơn vừa tạo
                }
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi thêm hóa đơn: " + e.getMessage());
        }
        
        return -1;
    }
    
    // Cập nhật thông tin hóa đơn
    public boolean capNhatHoaDon(HoaDon hoaDon) {
        String sql = "UPDATE HoaDon SET TongTien = ?, ThanhToan = ?, PhuongThucTT = ? WHERE MaHD = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setBigDecimal(1, hoaDon.getTongTien());
            stmt.setBigDecimal(2, hoaDon.getThanhToan());
            stmt.setString(3, hoaDon.getPhuongThucTT());
            stmt.setInt(4, hoaDon.getMaHD());
            
            return stmt.executeUpdate() > 0;
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi cập nhật hóa đơn: " + e.getMessage());
            return false;
        }
    }
    
    // Cập nhật thanh toán
    public boolean capNhatThanhToan(int maHD, BigDecimal soTienThanhToan, String phuongThuc) {
        String sql = "UPDATE HoaDon SET ThanhToan = ThanhToan + ?, PhuongThucTT = ? WHERE MaHD = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setBigDecimal(1, soTienThanhToan);
            stmt.setString(2, phuongThuc);
            stmt.setInt(3, maHD);
            
            return stmt.executeUpdate() > 0;
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi cập nhật thanh toán: " + e.getMessage());
            return false;
        }
    }
    
    // Lấy hóa đơn theo mã
    public HoaDon layHoaDonTheoMa(int maHD) {
        String sql = "SELECT hd.*, dh.NgayNhan, dh.NgayTra, kh.TenKhachHang, kh.SoDienThoai " +
                    "FROM HoaDon hd " +
                    "JOIN DonHang dh ON hd.MaDon = dh.MaDon " +
                    "JOIN KhachHang kh ON dh.MaKH = kh.MaKH " +
                    "WHERE hd.MaHD = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, maHD);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return taoHoaDonTuResultSet(rs);
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy hóa đơn theo mã: " + e.getMessage());
        }
        
        return null;
    }
    
    // Lấy hóa đơn theo đơn hàng
    public HoaDon layHoaDonTheoDonHang(int maDon) {
        String sql = "SELECT hd.*, dh.NgayNhan, dh.NgayTra, kh.TenKhachHang, kh.SoDienThoai " +
                    "FROM HoaDon hd " +
                    "JOIN DonHang dh ON hd.MaDon = dh.MaDon " +
                    "JOIN KhachHang kh ON dh.MaKH = kh.MaKH " +
                    "WHERE hd.MaDon = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, maDon);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return taoHoaDonTuResultSet(rs);
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy hóa đơn theo đơn hàng: " + e.getMessage());
        }
        
        return null;
    }
    
    // Lấy danh sách tất cả hóa đơn
    public List<HoaDon> layTatCaHoaDon() {
        List<HoaDon> danhSach = new ArrayList<>();
        String sql = "SELECT hd.*, dh.NgayNhan, dh.NgayTra, kh.TenKhachHang, kh.SoDienThoai " +
                    "FROM HoaDon hd " +
                    "JOIN DonHang dh ON hd.MaDon = dh.MaDon " +
                    "JOIN KhachHang kh ON dh.MaKH = kh.MaKH " +
                    "ORDER BY hd.NgayLap DESC";
        
        try (Connection conn = DBConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                danhSach.add(taoHoaDonTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy danh sách hóa đơn: " + e.getMessage());
        }
        
        return danhSach;
    }
    
    // Lấy hóa đơn theo trạng thái thanh toán
    public List<HoaDon> layHoaDonTheoTrangThai(String trangThai) {
        List<HoaDon> danhSach = new ArrayList<>();
        String sql = "SELECT hd.*, dh.NgayNhan, dh.NgayTra, kh.TenKhachHang, kh.SoDienThoai " +
                    "FROM HoaDon hd " +
                    "JOIN DonHang dh ON hd.MaDon = dh.MaDon " +
                    "JOIN KhachHang kh ON dh.MaKH = kh.MaKH " +
                    "WHERE hd.TrangThai = ? ORDER BY hd.NgayLap DESC";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, trangThai);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                danhSach.add(taoHoaDonTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy hóa đơn theo trạng thái: " + e.getMessage());
        }
        
        return danhSach;
    }
    
    // Lấy hóa đơn theo khoảng thời gian
    public List<HoaDon> layHoaDonTheoKhoangThoiGian(LocalDateTime tuNgay, LocalDateTime denNgay) {
        List<HoaDon> danhSach = new ArrayList<>();
        String sql = "SELECT hd.*, dh.NgayNhan, dh.NgayTra, kh.TenKhachHang, kh.SoDienThoai " +
                    "FROM HoaDon hd " +
                    "JOIN DonHang dh ON hd.MaDon = dh.MaDon " +
                    "JOIN KhachHang kh ON dh.MaKH = kh.MaKH " +
                    "WHERE hd.NgayLap BETWEEN ? AND ? ORDER BY hd.NgayLap DESC";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setTimestamp(1, Timestamp.valueOf(tuNgay));
            stmt.setTimestamp(2, Timestamp.valueOf(denNgay));
            
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                danhSach.add(taoHoaDonTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy hóa đơn theo khoảng thời gian: " + e.getMessage());
        }
        
        return danhSach;
    }
    
    // Tính tổng doanh thu theo ngày
    public BigDecimal tinhDoanhThuTheoNgay(LocalDateTime ngay) {
        String sql = "SELECT SUM(ThanhToan) FROM HoaDon WHERE CAST(NgayLap AS DATE) = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setDate(1, Date.valueOf(ngay.toLocalDate()));
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                BigDecimal result = rs.getBigDecimal(1);
                return result != null ? result : BigDecimal.ZERO;
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi tính doanh thu theo ngày: " + e.getMessage());
        }
        
        return BigDecimal.ZERO;
    }
    
    // Tính tổng doanh thu theo tháng
    public BigDecimal tinhDoanhThuTheoThang(int thang, int nam) {
        String sql = "SELECT SUM(ThanhToan) FROM HoaDon WHERE MONTH(NgayLap) = ? AND YEAR(NgayLap) = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, thang);
            stmt.setInt(2, nam);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                BigDecimal result = rs.getBigDecimal(1);
                return result != null ? result : BigDecimal.ZERO;
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi tính doanh thu theo tháng: " + e.getMessage());
        }
        
        return BigDecimal.ZERO;
    }
    
    // Đếm số hóa đơn theo trạng thái
    public int demHoaDonTheoTrangThai(String trangThai) {
        String sql = "SELECT COUNT(*) FROM HoaDon WHERE TrangThai = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, trangThai);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1);
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi đếm hóa đơn theo trạng thái: " + e.getMessage());
        }
        
        return 0;
    }

    // Kiểm tra đơn hàng đã có hóa đơn chưa
    public boolean kiemTraDonHangCoHoaDon(int maDon) {
        String sql = "SELECT COUNT(*) FROM HoaDon WHERE MaDon = ?";

        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setInt(1, maDon);
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                return rs.getInt(1) > 0;
            }

        } catch (SQLException e) {
            System.err.println("Lỗi khi kiểm tra đơn hàng có hóa đơn: " + e.getMessage());
        }

        return false;
    }

    // Phương thức helper để tạo đối tượng HoaDon từ ResultSet
    private HoaDon taoHoaDonTuResultSet(ResultSet rs) throws SQLException {
        HoaDon hd = new HoaDon();
        hd.setMaHD(rs.getInt("MaHD"));
        hd.setMaDon(rs.getInt("MaDon"));

        Timestamp ngayLap = rs.getTimestamp("NgayLap");
        if (ngayLap != null) {
            hd.setNgayLap(ngayLap.toLocalDateTime());
        }

        hd.setTongTien(rs.getBigDecimal("TongTien"));
        hd.setThanhToan(rs.getBigDecimal("ThanhToan"));
        hd.setConLai(rs.getBigDecimal("ConLai"));
        hd.setPhuongThucTT(rs.getString("PhuongThucTT"));
        hd.setTrangThai(rs.getString("TrangThai"));
        hd.setNguoiLap(rs.getString("NguoiLap"));

        // Thông tin đơn hàng và khách hàng
        Timestamp ngayNhan = rs.getTimestamp("NgayNhan");
        if (ngayNhan != null) {
            hd.setNgayNhan(ngayNhan.toLocalDateTime());
        }

        Timestamp ngayTra = rs.getTimestamp("NgayTra");
        if (ngayTra != null) {
            hd.setNgayTra(ngayTra.toLocalDateTime());
        }

        hd.setTenKhachHang(rs.getString("TenKhachHang"));
        hd.setSoDienThoai(rs.getString("SoDienThoai"));

        return hd;
    }
}
