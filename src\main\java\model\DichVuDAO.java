package model;

import util.DBConnection;
import java.sql.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * DAO class cho Dịch vụ
 * <AUTHOR>
 */
public class DichVuDAO {
    
    // Thêm dịch vụ mới
    public boolean themDichVu(DichVu dichVu) {
        String sql = "INSERT INTO DichVu (TenDichVu, DonGia, DonVi, MoTa) VALUES (?, ?, ?, ?)";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, dichVu.getTenDichVu());
            stmt.setBigDecimal(2, dichVu.getDonGia());
            stmt.setString(3, dichVu.getDonVi());
            stmt.setString(4, dichVu.getMoTa());
            
            return stmt.executeUpdate() > 0;
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi thêm dịch vụ: " + e.getMessage());
            return false;
        }
    }
    
    // Cập nhật thông tin dịch vụ
    public boolean capNhatDichVu(DichVu dichVu) {
        String sql = "UPDATE DichVu SET TenDichVu = ?, DonGia = ?, DonVi = ?, MoTa = ? WHERE MaDV = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, dichVu.getTenDichVu());
            stmt.setBigDecimal(2, dichVu.getDonGia());
            stmt.setString(3, dichVu.getDonVi());
            stmt.setString(4, dichVu.getMoTa());
            stmt.setInt(5, dichVu.getMaDV());
            
            return stmt.executeUpdate() > 0;
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi cập nhật dịch vụ: " + e.getMessage());
            return false;
        }
    }
    
    // Xóa dịch vụ (soft delete)
    public boolean xoaDichVu(int maDV) {
        String sql = "UPDATE DichVu SET TrangThai = 0 WHERE MaDV = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, maDV);
            return stmt.executeUpdate() > 0;
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi xóa dịch vụ: " + e.getMessage());
            return false;
        }
    }
    
    // Lấy dịch vụ theo mã
    public DichVu layDichVuTheoMa(int maDV) {
        String sql = "SELECT * FROM DichVu WHERE MaDV = ? AND TrangThai = 1";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, maDV);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return taoDichVuTuResultSet(rs);
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy dịch vụ theo mã: " + e.getMessage());
        }
        
        return null;
    }
    
    // Lấy dịch vụ theo tên
    public DichVu layDichVuTheoTen(String tenDichVu) {
        String sql = "SELECT * FROM DichVu WHERE TenDichVu = ? AND TrangThai = 1";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, tenDichVu);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return taoDichVuTuResultSet(rs);
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy dịch vụ theo tên: " + e.getMessage());
        }
        
        return null;
    }
    
    // Lấy danh sách tất cả dịch vụ
    public List<DichVu> layTatCaDichVu() {
        List<DichVu> danhSach = new ArrayList<>();
        String sql = "SELECT * FROM DichVu WHERE TrangThai = 1 ORDER BY TenDichVu";
        
        try (Connection conn = DBConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                danhSach.add(taoDichVuTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy danh sách dịch vụ: " + e.getMessage());
        }
        
        return danhSach;
    }
    
    // Tìm kiếm dịch vụ theo tên
    public List<DichVu> timKiemDichVu(String tuKhoa) {
        List<DichVu> danhSach = new ArrayList<>();
        String sql = "SELECT * FROM DichVu WHERE TrangThai = 1 AND TenDichVu LIKE ? ORDER BY TenDichVu";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, "%" + tuKhoa + "%");
            
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                danhSach.add(taoDichVuTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi tìm kiếm dịch vụ: " + e.getMessage());
        }
        
        return danhSach;
    }
    
    // Kiểm tra tên dịch vụ đã tồn tại chưa
    public boolean kiemTraTenDichVuTonTai(String tenDichVu, int maDVLoaiTru) {
        String sql = "SELECT COUNT(*) FROM DichVu WHERE TenDichVu = ? AND MaDV != ? AND TrangThai = 1";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, tenDichVu);
            stmt.setInt(2, maDVLoaiTru);
            
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi kiểm tra tên dịch vụ tồn tại: " + e.getMessage());
        }
        
        return false;
    }
    
    // Đếm tổng số dịch vụ
    public int demTongSoDichVu() {
        String sql = "SELECT COUNT(*) FROM DichVu WHERE TrangThai = 1";
        
        try (Connection conn = DBConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            if (rs.next()) {
                return rs.getInt(1);
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi đếm dịch vụ: " + e.getMessage());
        }
        
        return 0;
    }
    
    // Lấy danh sách tên dịch vụ (cho ComboBox)
    public List<String> layDanhSachTenDichVu() {
        List<String> danhSach = new ArrayList<>();
        String sql = "SELECT TenDichVu FROM DichVu WHERE TrangThai = 1 ORDER BY TenDichVu";
        
        try (Connection conn = DBConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                danhSach.add(rs.getString("TenDichVu"));
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy danh sách tên dịch vụ: " + e.getMessage());
        }
        
        return danhSach;
    }
    
    // Phương thức helper để tạo đối tượng DichVu từ ResultSet
    private DichVu taoDichVuTuResultSet(ResultSet rs) throws SQLException {
        DichVu dv = new DichVu();
        dv.setMaDV(rs.getInt("MaDV"));
        dv.setTenDichVu(rs.getString("TenDichVu"));
        dv.setDonGia(rs.getBigDecimal("DonGia"));
        dv.setDonVi(rs.getString("DonVi"));
        dv.setMoTa(rs.getString("MoTa"));
        dv.setTrangThai(rs.getBoolean("TrangThai"));
        
        return dv;
    }
}
