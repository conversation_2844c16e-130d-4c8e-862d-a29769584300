package model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Model class cho Hóa đơn
 * <AUTHOR>
 */
public class HoaDon {
    private int maHD;
    private int maDon;
    private LocalDateTime ngayLap;
    private BigDecimal tongTien;
    private BigDecimal thanhToan;
    private BigDecimal conLai;
    private String phuongThucTT;
    private String trangThai;
    private String nguoiLap;
    
    // Thông tin đơn hàng và khách hàng (để hiển thị)
    private String tenKhachHang;
    private String soDienThoai;
    private LocalDateTime ngayNhan;
    private LocalDateTime ngayTra;

    // Constructor mặc định
    public HoaDon() {
        this.ngayLap = LocalDateTime.now();
        this.tongTien = BigDecimal.ZERO;
        this.thanhToan = BigDecimal.ZERO;
        this.conLai = BigDecimal.ZERO;
        this.phuongThucTT = "Tiền mặt";
        this.trangThai = "Chưa thanh toán";
    }

    // Constructor đầy đủ
    public HoaDon(int maHD, int maDon, BigDecimal tongTien, BigDecimal thanhToan, 
                  String phuongThucTT, String nguoiLap) {
        this.maHD = maHD;
        this.maDon = maDon;
        this.ngayLap = LocalDateTime.now();
        this.tongTien = tongTien;
        this.thanhToan = thanhToan;
        this.phuongThucTT = phuongThucTT;
        this.nguoiLap = nguoiLap;
        tinhConLai();
        capNhatTrangThai();
    }

    // Constructor không có mã (dùng khi thêm mới)
    public HoaDon(int maDon, BigDecimal tongTien, BigDecimal thanhToan, 
                  String phuongThucTT, String nguoiLap) {
        this.maDon = maDon;
        this.ngayLap = LocalDateTime.now();
        this.tongTien = tongTien;
        this.thanhToan = thanhToan;
        this.phuongThucTT = phuongThucTT;
        this.nguoiLap = nguoiLap;
        tinhConLai();
        capNhatTrangThai();
    }

    // Constructor với double (tiện lợi)
    public HoaDon(int maDon, double tongTien, double thanhToan, 
                  String phuongThucTT, String nguoiLap) {
        this.maDon = maDon;
        this.ngayLap = LocalDateTime.now();
        this.tongTien = BigDecimal.valueOf(tongTien);
        this.thanhToan = BigDecimal.valueOf(thanhToan);
        this.phuongThucTT = phuongThucTT;
        this.nguoiLap = nguoiLap;
        tinhConLai();
        capNhatTrangThai();
    }

    // Getters và Setters
    public int getMaHD() {
        return maHD;
    }

    public void setMaHD(int maHD) {
        this.maHD = maHD;
    }

    public int getMaDon() {
        return maDon;
    }

    public void setMaDon(int maDon) {
        this.maDon = maDon;
    }

    public LocalDateTime getNgayLap() {
        return ngayLap;
    }

    public void setNgayLap(LocalDateTime ngayLap) {
        this.ngayLap = ngayLap;
    }

    public BigDecimal getTongTien() {
        return tongTien;
    }

    public void setTongTien(BigDecimal tongTien) {
        this.tongTien = tongTien;
        tinhConLai();
        capNhatTrangThai();
    }

    public void setTongTien(double tongTien) {
        this.tongTien = BigDecimal.valueOf(tongTien);
        tinhConLai();
        capNhatTrangThai();
    }

    public BigDecimal getThanhToan() {
        return thanhToan;
    }

    public void setThanhToan(BigDecimal thanhToan) {
        this.thanhToan = thanhToan;
        tinhConLai();
        capNhatTrangThai();
    }

    public void setThanhToan(double thanhToan) {
        this.thanhToan = BigDecimal.valueOf(thanhToan);
        tinhConLai();
        capNhatTrangThai();
    }

    public BigDecimal getConLai() {
        return conLai;
    }

    public void setConLai(BigDecimal conLai) {
        this.conLai = conLai;
    }

    public String getPhuongThucTT() {
        return phuongThucTT;
    }

    public void setPhuongThucTT(String phuongThucTT) {
        this.phuongThucTT = phuongThucTT;
    }

    public String getTrangThai() {
        return trangThai;
    }

    public void setTrangThai(String trangThai) {
        this.trangThai = trangThai;
    }

    public String getNguoiLap() {
        return nguoiLap;
    }

    public void setNguoiLap(String nguoiLap) {
        this.nguoiLap = nguoiLap;
    }

    public String getTenKhachHang() {
        return tenKhachHang;
    }

    public void setTenKhachHang(String tenKhachHang) {
        this.tenKhachHang = tenKhachHang;
    }

    public String getSoDienThoai() {
        return soDienThoai;
    }

    public void setSoDienThoai(String soDienThoai) {
        this.soDienThoai = soDienThoai;
    }

    public LocalDateTime getNgayNhan() {
        return ngayNhan;
    }

    public void setNgayNhan(LocalDateTime ngayNhan) {
        this.ngayNhan = ngayNhan;
    }

    public LocalDateTime getNgayTra() {
        return ngayTra;
    }

    public void setNgayTra(LocalDateTime ngayTra) {
        this.ngayTra = ngayTra;
    }

    // Phương thức tính số tiền còn lại
    public void tinhConLai() {
        if (tongTien != null && thanhToan != null) {
            this.conLai = tongTien.subtract(thanhToan);
        } else {
            this.conLai = BigDecimal.ZERO;
        }
    }

    // Phương thức cập nhật trạng thái thanh toán
    public void capNhatTrangThai() {
        if (thanhToan != null && tongTien != null) {
            if (thanhToan.compareTo(tongTien) >= 0) {
                this.trangThai = "Đã thanh toán";
            } else if (thanhToan.compareTo(BigDecimal.ZERO) > 0) {
                this.trangThai = "Thanh toán một phần";
            } else {
                this.trangThai = "Chưa thanh toán";
            }
        }
    }

    // Phương thức thanh toán thêm
    public void thanhToanThem(BigDecimal soTien) {
        if (soTien != null && soTien.compareTo(BigDecimal.ZERO) > 0) {
            this.thanhToan = this.thanhToan.add(soTien);
            tinhConLai();
            capNhatTrangThai();
        }
    }

    public void thanhToanThem(double soTien) {
        thanhToanThem(BigDecimal.valueOf(soTien));
    }

    // Kiểm tra đã thanh toán đủ chưa
    public boolean daThanhToanDu() {
        return "Đã thanh toán".equals(trangThai);
    }

    @Override
    public String toString() {
        return "HoaDon{" +
                "maHD=" + maHD +
                ", maDon=" + maDon +
                ", ngayLap=" + ngayLap +
                ", tongTien=" + tongTien +
                ", thanhToan=" + thanhToan +
                ", conLai=" + conLai +
                ", phuongThucTT='" + phuongThucTT + '\'' +
                ", trangThai='" + trangThai + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        HoaDon hoaDon = (HoaDon) obj;
        return maHD == hoaDon.maHD;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(maHD);
    }
}
