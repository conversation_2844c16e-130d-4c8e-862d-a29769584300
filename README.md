# Ứng Dụng Quản Lý Giặt Ủi

## Mô tả
Ứng dụng Java Swing để quản lý tiệm giặt ủi với các chức năng:
- Quản lý khách hàng
- Quản lý dịch vụ giặt ủi
- Quản lý đơn hàng
- <PERSON>h toán hóa đơn
- Thống kê doanh thu
- Tra cứu đơn hàng

## Công nghệ sử dụng
- **Java 11+**
- **Java Swing** (Giao diện)
- **SQL Server** (Cơ sở dữ liệu)
- **Maven** (Quản lý dependencies)

## Cấu trúc dự án
```
src/
├── main/java/
│   ├── model/          # Các class Model và DAO
│   │   ├── KhachHang.java
│   │   ├── KhachHangDAO.java
│   │   ├── DichVu.java
│   │   ├── DichVuDAO.java
│   │   ├── DonHang.java
│   │   ├── DonHangDAO.java
│   │   ├── ChiTietDonHang.java
│   │   ├── ChiTietDonHangDAO.java
│   │   ├── HoaDon.java
│   │   └── HoaDonDAO.java
│   ├── view/           # Các class giao diện
│   │   ├── MainFrame.java
│   │   ├── QuanLyKhachHang.java
│   │   ├── QuanLyDichVu.java
│   │   ├── QuanLyDonHang.java
│   │   ├── ThanhToanHoaDon.java
│   │   ├── ThongKeDoanhThu.java
│   │   ├── ThongKeDonHang.java
│   │   └── TraCuuDonHang.java
│   └── util/           # Utilities
│       └── DBConnection.java
database/
├── QuanLyGiatUi_Schema.sql    # Script tạo database
├── SampleData.sql             # Dữ liệu mẫu
└── RunDatabase.bat            # Script chạy database
```

## Cài đặt và chạy

### 1. Yêu cầu hệ thống
- Java JDK 11 trở lên
- SQL Server (LocalDB hoặc SQL Server Express)
- Maven 3.6+

### 2. Thiết lập database
1. Đảm bảo SQL Server đang chạy
2. Chạy script tạo database:
   ```bash
   cd database
   RunDatabase.bat
   ```
   Hoặc chạy thủ công:
   ```sql
   sqlcmd -S localhost -U sa -P 123456 -i QuanLyGiatUi_Schema.sql
   sqlcmd -S localhost -U sa -P 123456 -i SampleData.sql
   ```

### 3. Cấu hình kết nối database
Kiểm tra file `src/main/java/util/DBConnection.java`:
```java
private static final String URL = "***************************************************************************************************;";
private static final String USER = "sa";
private static final String PASS = "123456";
```

### 4. Chạy ứng dụng
```bash
mvn clean compile exec:java
```

Hoặc chạy từ IDE:
- Main class: `view.MainFrame`

## Chức năng chính

### 1. Quản lý khách hàng
- Thêm, sửa, xóa khách hàng
- Tìm kiếm khách hàng theo tên/SĐT
- Validation dữ liệu đầu vào

### 2. Quản lý dịch vụ
- Quản lý các loại dịch vụ giặt ủi
- Thiết lập đơn giá cho từng dịch vụ
- Phân loại theo đơn vị (kg, bộ, cái)

### 3. Quản lý đơn hàng
- Tạo đơn hàng mới
- Cập nhật trạng thái đơn hàng
- Tính toán tự động tổng tiền

### 4. Thanh toán
- Tạo hóa đơn thanh toán
- Hỗ trợ nhiều phương thức thanh toán
- In hóa đơn

### 5. Thống kê
- Báo cáo doanh thu theo ngày/tháng
- Thống kê số lượng đơn hàng
- Phân tích theo dịch vụ

### 6. Tra cứu
- Tìm kiếm đơn hàng theo nhiều tiêu chí
- Lọc theo trạng thái, khách hàng

## Database Schema

### Bảng chính:
- **KhachHang**: Thông tin khách hàng
- **DichVu**: Danh mục dịch vụ
- **DonHang**: Đơn hàng chính
- **ChiTietDonHang**: Chi tiết từng dịch vụ trong đơn
- **HoaDon**: Hóa đơn thanh toán

### Quan hệ:
- KhachHang (1) → (n) DonHang
- DonHang (1) → (n) ChiTietDonHang
- DichVu (1) → (n) ChiTietDonHang
- DonHang (1) → (1) HoaDon

## Dữ liệu mẫu
Database được tạo với dữ liệu mẫu bao gồm:
- 5 khách hàng
- 7 dịch vụ giặt ủi
- 5 đơn hàng với chi tiết
- 5 hóa đơn tương ứng

## Troubleshooting

### Lỗi kết nối database:
1. Kiểm tra SQL Server đang chạy
2. Xác nhận thông tin kết nối trong `DBConnection.java`
3. Kiểm tra firewall và port 1433

### Lỗi Maven:
```bash
mvn clean install
```

### Lỗi Java version:
Đảm bảo sử dụng Java 11+:
```bash
java -version
javac -version
```

## Tác giả
- Phát triển bởi nhóm sinh viên
- Môn: Lập trình Java
- Năm: 2024
