-- Script tạo database và bảng cho hệ thống Quản Lý Giặt Ủi
-- Tạo database
USE master;
GO

IF EXISTS (SELECT name FROM sys.databases WHERE name = 'QuanLyGiatUi')
    DROP DATABASE QuanLyGiatUi;
GO

CREATE DATABASE QuanLyGiatUi;
GO

USE QuanLyGiatUi;
GO

-- <PERSON><PERSON><PERSON> Khách hàng
CREATE TABLE KhachHang (
    MaKH INT IDENTITY(1,1) PRIMARY KEY,
    TenKhachHang NVARCHAR(100) NOT NULL,
    SoDienThoai VARCHAR(15) NOT NULL UNIQUE,
    DiaChi NVARCHAR(200),
    Email VARCHAR(100),
    NgayTao DATETIME DEFAULT GETDATE(),
    TrangThai BIT DEFAULT 1 -- 1: Ho<PERSON>t động, 0: Không hoạt động
);

-- Bảng Dịch vụ
CREATE TABLE DichVu (
    MaDV INT IDENTITY(1,1) PRIMARY KEY,
    TenDichVu NVARCHAR(100) NOT NULL,
    DonGia DECIMAL(10,2) NOT NULL,
    DonVi NVARCHAR(20) DEFAULT N'kg', -- kg, bộ, cái
    MoTa NVARCHAR(500),
    TrangThai BIT DEFAULT 1
);

-- Bảng Đơn hàng
CREATE TABLE DonHang (
    MaDon INT IDENTITY(1,1) PRIMARY KEY,
    MaKH INT NOT NULL,
    NgayNhan DATETIME NOT NULL,
    NgayTra DATETIME,
    NgayHenTra DATETIME,
    TongTien DECIMAL(12,2) DEFAULT 0,
    TrangThai NVARCHAR(50) DEFAULT N'Đang xử lý', -- Đang xử lý, Đã hoàn thành, Đã hủy
    GhiChu NVARCHAR(500),
    NguoiTao NVARCHAR(50),
    NgayTao DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (MaKH) REFERENCES KhachHang(MaKH)
);

-- Bảng Chi tiết đơn hàng
CREATE TABLE ChiTietDonHang (
    MaChiTiet INT IDENTITY(1,1) PRIMARY KEY,
    MaDon INT NOT NULL,
    MaDV INT NOT NULL,
    SoLuong DECIMAL(8,2) NOT NULL,
    DonGia DECIMAL(10,2) NOT NULL,
    ThanhTien DECIMAL(12,2) NOT NULL,
    GhiChu NVARCHAR(200),
    FOREIGN KEY (MaDon) REFERENCES DonHang(MaDon),
    FOREIGN KEY (MaDV) REFERENCES DichVu(MaDV)
);

-- Bảng Hóa đơn
CREATE TABLE HoaDon (
    MaHD INT IDENTITY(1,1) PRIMARY KEY,
    MaDon INT NOT NULL,
    NgayLap DATETIME DEFAULT GETDATE(),
    TongTien DECIMAL(12,2) NOT NULL,
    ThanhToan DECIMAL(12,2) DEFAULT 0,
    ConLai DECIMAL(12,2) DEFAULT 0,
    PhuongThucTT NVARCHAR(50) DEFAULT N'Tiền mặt', -- Tiền mặt, Chuyển khoản, Thẻ
    TrangThai NVARCHAR(50) DEFAULT N'Chưa thanh toán', -- Chưa thanh toán, Đã thanh toán, Thanh toán một phần
    NguoiLap NVARCHAR(50),
    FOREIGN KEY (MaDon) REFERENCES DonHang(MaDon)
);

-- Trigger tự động tính tổng tiền đơn hàng
CREATE TRIGGER trg_UpdateTongTienDonHang
ON ChiTietDonHang
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    UPDATE DonHang 
    SET TongTien = (
        SELECT ISNULL(SUM(ThanhTien), 0) 
        FROM ChiTietDonHang 
        WHERE MaDon = DonHang.MaDon
    )
    WHERE MaDon IN (
        SELECT DISTINCT MaDon FROM inserted
        UNION
        SELECT DISTINCT MaDon FROM deleted
    );
END;
GO

-- Trigger tự động cập nhật số tiền còn lại trong hóa đơn
CREATE TRIGGER trg_UpdateConLaiHoaDon
ON HoaDon
AFTER INSERT, UPDATE
AS
BEGIN
    UPDATE HoaDon 
    SET ConLai = TongTien - ThanhToan,
        TrangThai = CASE 
            WHEN ThanhToan >= TongTien THEN N'Đã thanh toán'
            WHEN ThanhToan > 0 THEN N'Thanh toán một phần'
            ELSE N'Chưa thanh toán'
        END
    WHERE MaHD IN (SELECT MaHD FROM inserted);
END;
GO
