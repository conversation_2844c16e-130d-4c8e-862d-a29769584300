<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JPanelFormInfo">
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Component id="jPanel1" alignment="0" max="32767" attributes="0"/>
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace min="-2" pref="78" max="-2" attributes="0"/>
              <Component id="jLabel2" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="32767" attributes="0"/>
          </Group>
          <Group type="103" rootIndex="1" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="106" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Component id="jLabel6" alignment="0" min="-2" max="-2" attributes="0"/>
                      <Component id="jLabel3" alignment="0" min="-2" max="-2" attributes="0"/>
                      <Component id="jLabel4" alignment="0" min="-2" max="-2" attributes="0"/>
                      <Component id="jLabel5" alignment="0" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="59" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="1" max="-2" attributes="0">
                      <Group type="102" alignment="1" attributes="0">
                          <Component id="cbotrangthaidonhang" min="-2" pref="479" max="-2" attributes="0"/>
                          <EmptySpace pref="85" max="32767" attributes="0"/>
                          <Component id="btntimkiem" min="-2" pref="121" max="-2" attributes="0"/>
                      </Group>
                      <Component id="txtmadonhang" alignment="0" max="32767" attributes="0"/>
                      <Component id="txthoten" alignment="0" max="32767" attributes="0"/>
                      <Component id="txtsdt" alignment="0" max="32767" attributes="0"/>
                  </Group>
                  <EmptySpace pref="81" max="32767" attributes="0"/>
              </Group>
              <Group type="102" alignment="0" attributes="0">
                  <Component id="jScrollPane1" max="32767" attributes="0"/>
                  <EmptySpace max="-2" attributes="0"/>
              </Group>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <Component id="jPanel1" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="216" max="-2" attributes="0"/>
              <Component id="jLabel2" min="-2" pref="30" max="-2" attributes="0"/>
              <EmptySpace min="0" pref="308" max="32767" attributes="0"/>
          </Group>
          <Group type="103" rootIndex="1" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="83" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="jLabel3" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="txtmadonhang" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace type="separate" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="jLabel4" alignment="3" min="-2" pref="22" max="-2" attributes="0"/>
                      <Component id="txthoten" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace type="separate" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="txtsdt" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                      <Component id="jLabel5" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace type="separate" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="103" alignment="0" groupAlignment="3" attributes="0">
                          <Component id="cbotrangthaidonhang" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                          <Component id="jLabel6" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <Component id="btntimkiem" alignment="0" min="-2" pref="30" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="62" max="-2" attributes="0"/>
                  <Component id="jScrollPane1" min="-2" pref="292" max="-2" attributes="0"/>
                  <EmptySpace max="32767" attributes="0"/>
              </Group>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="jPanel1">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="389" max="-2" attributes="0"/>
                  <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                  <EmptySpace pref="436" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="1" attributes="0">
                  <EmptySpace pref="16" max="32767" attributes="0"/>
                  <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="15" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JLabel" name="jLabel1">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="24" style="0"/>
            </Property>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="0" red="0" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Tra C&#x1ee9;u &#x110;&#x1a1;n H&#xe0;ng "/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="M&#xe3; &#x110;&#x1a1;n"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel4">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="H&#x1ecd; T&#xea;n "/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel5">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="S&#x1ed1; &#x110;i&#x1ec7;n Tho&#x1ea1;i"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel6">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Tr&#x1ea1;ng Th&#xe1;i"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JComboBox" name="cbotrangthaidonhang">
      <Properties>
        <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
          <StringArray count="4">
            <StringItem index="0" value="Item 1"/>
            <StringItem index="1" value="Item 2"/>
            <StringItem index="2" value="Item 3"/>
            <StringItem index="3" value="Item 4"/>
          </StringArray>
        </Property>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_TypeParameters" type="java.lang.String" value="&lt;String&gt;"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JTextField" name="txtmadonhang">
    </Component>
    <Component class="javax.swing.JTextField" name="txthoten">
    </Component>
    <Component class="javax.swing.JTextField" name="txtsdt">
    </Component>
    <Component class="javax.swing.JButton" name="btntimkiem">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="T&#xec;m Ki&#x1ebf;m"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btntimkiemActionPerformed"/>
      </Events>
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTable" name="tblthongtintracuudonhang">
          <Properties>
            <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
              <Table columnCount="4" rowCount="3">
                <Column editable="true" title="T&#xea;n Kh&#xe1;ch H&#xe0;ng" type="java.lang.Object"/>
                <Column editable="true" title="M&#xe3; &#x110;&#x1a1;n" type="java.lang.Object"/>
                <Column editable="true" title="S&#x1ed1; &#x110;i&#x1ec7;n Tho&#x1ea1;i" type="java.lang.Object"/>
                <Column editable="true" title="Tr&#x1ea1;ng Th&#xe1;i" type="java.lang.Object"/>
              </Table>
            </Property>
            <Property name="columnModel" type="javax.swing.table.TableColumnModel" editor="org.netbeans.modules.form.editors2.TableColumnModelEditor">
              <TableColumnModel selectionModel="0">
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
              </TableColumnModel>
            </Property>
            <Property name="tableHeader" type="javax.swing.table.JTableHeader" editor="org.netbeans.modules.form.editors2.JTableHeaderEditor">
              <TableHeader reorderingAllowed="true" resizingAllowed="true"/>
            </Property>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Th&#xf4;ng Tin Tra C&#x1ee9;u &#x110;&#x1a1;n H&#xe0;ng"/>
      </Properties>
    </Component>
  </SubComponents>
</Form>
