package model;

import util.DBConnection;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * DAO class cho Khách hàng
 * <AUTHOR>
 */
public class KhachHangDAO {

    // Thêm khách hàng mới
    public boolean themKhachHang(KhachHang khachHang) {
        String sql = "INSERT INTO KhachHang (TenKhachHang, SoDienThoai, DiaChi, Email) VALUES (?, ?, ?, ?)";

        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, khachHang.getTenKhachHang());
            stmt.setString(2, khachHang.getSoDienThoai());
            stmt.setString(3, khachHang.getDiaChi());
            stmt.setString(4, khachHang.getEmail());

            return stmt.executeUpdate() > 0;

        } catch (SQLException e) {
            System.err.println("Lỗi khi thêm khách hàng: " + e.getMessage());
            return false;
        }
    }

    // Cập nhật thông tin khách hàng
    public boolean capNhatKhachHang(KhachHang khachHang) {
        String sql = "UPDATE KhachHang SET TenKhachHang = ?, SoDienThoai = ?, DiaChi = ?, Email = ? WHERE MaKH = ?";

        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, khachHang.getTenKhachHang());
            stmt.setString(2, khachHang.getSoDienThoai());
            stmt.setString(3, khachHang.getDiaChi());
            stmt.setString(4, khachHang.getEmail());
            stmt.setInt(5, khachHang.getMaKH());

            return stmt.executeUpdate() > 0;

        } catch (SQLException e) {
            System.err.println("Lỗi khi cập nhật khách hàng: " + e.getMessage());
            return false;
        }
    }

    // Xóa khách hàng (soft delete)
    public boolean xoaKhachHang(int maKH) {
        String sql = "UPDATE KhachHang SET TrangThai = 0 WHERE MaKH = ?";

        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setInt(1, maKH);
            return stmt.executeUpdate() > 0;

        } catch (SQLException e) {
            System.err.println("Lỗi khi xóa khách hàng: " + e.getMessage());
            return false;
        }
    }

    // Lấy khách hàng theo mã
    public KhachHang layKhachHangTheoMa(int maKH) {
        String sql = "SELECT * FROM KhachHang WHERE MaKH = ? AND TrangThai = 1";

        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setInt(1, maKH);
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                return taoKhachHangTuResultSet(rs);
            }

        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy khách hàng theo mã: " + e.getMessage());
        }

        return null;
    }

    // Lấy khách hàng theo số điện thoại
    public KhachHang layKhachHangTheoSDT(String soDienThoai) {
        String sql = "SELECT * FROM KhachHang WHERE SoDienThoai = ? AND TrangThai = 1";

        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, soDienThoai);
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                return taoKhachHangTuResultSet(rs);
            }

        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy khách hàng theo SDT: " + e.getMessage());
        }

        return null;
    }

    // Lấy danh sách tất cả khách hàng
    public List<KhachHang> layTatCaKhachHang() {
        List<KhachHang> danhSach = new ArrayList<>();
        String sql = "SELECT * FROM KhachHang WHERE TrangThai = 1 ORDER BY NgayTao DESC";

        try (Connection conn = DBConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                danhSach.add(taoKhachHangTuResultSet(rs));
            }

        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy danh sách khách hàng: " + e.getMessage());
        }

        return danhSach;
    }

    // Tìm kiếm khách hàng theo tên hoặc số điện thoại
    public List<KhachHang> timKiemKhachHang(String tuKhoa) {
        List<KhachHang> danhSach = new ArrayList<>();
        String sql = "SELECT * FROM KhachHang WHERE TrangThai = 1 AND " +
                    "(TenKhachHang LIKE ? OR SoDienThoai LIKE ?) ORDER BY TenKhachHang";

        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            String timKiem = "%" + tuKhoa + "%";
            stmt.setString(1, timKiem);
            stmt.setString(2, timKiem);

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                danhSach.add(taoKhachHangTuResultSet(rs));
            }

        } catch (SQLException e) {
            System.err.println("Lỗi khi tìm kiếm khách hàng: " + e.getMessage());
        }

        return danhSach;
    }

    // Kiểm tra số điện thoại đã tồn tại chưa
    public boolean kiemTraSDTTonTai(String soDienThoai, int maKHLoaiTru) {
        String sql = "SELECT COUNT(*) FROM KhachHang WHERE SoDienThoai = ? AND MaKH != ? AND TrangThai = 1";

        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, soDienThoai);
            stmt.setInt(2, maKHLoaiTru);

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }

        } catch (SQLException e) {
            System.err.println("Lỗi khi kiểm tra SDT tồn tại: " + e.getMessage());
        }

        return false;
    }

    // Đếm tổng số khách hàng
    public int demTongSoKhachHang() {
        String sql = "SELECT COUNT(*) FROM KhachHang WHERE TrangThai = 1";

        try (Connection conn = DBConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                return rs.getInt(1);
            }

        } catch (SQLException e) {
            System.err.println("Lỗi khi đếm khách hàng: " + e.getMessage());
        }

        return 0;
    }

    // Phương thức helper để tạo đối tượng KhachHang từ ResultSet
    private KhachHang taoKhachHangTuResultSet(ResultSet rs) throws SQLException {
        KhachHang kh = new KhachHang();
        kh.setMaKH(rs.getInt("MaKH"));
        kh.setTenKhachHang(rs.getString("TenKhachHang"));
        kh.setSoDienThoai(rs.getString("SoDienThoai"));
        kh.setDiaChi(rs.getString("DiaChi"));
        kh.setEmail(rs.getString("Email"));

        Timestamp ngayTao = rs.getTimestamp("NgayTao");
        if (ngayTao != null) {
            kh.setNgayTao(ngayTao.toLocalDateTime());
        }

        kh.setTrangThai(rs.getBoolean("TrangThai"));

        return kh;
    }
}
