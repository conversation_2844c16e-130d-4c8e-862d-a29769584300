package model;

import util.DBConnection;
import java.sql.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * DAO class cho Đơn hàng
 * <AUTHOR>
 */
public class DonHangDAO {
    
    // Thêm đơn hàng mới
    public int themDonHang(DonHang donHang) {
        String sql = "INSERT INTO DonHang (MaKH, NgayNhan, NgayHenTra, TrangThai, GhiChu, NguoiTao) " +
                    "VALUES (?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setInt(1, donHang.getMaKH());
            stmt.setTimestamp(2, Timestamp.valueOf(donHang.getNgayNhan()));
            stmt.setTimestamp(3, Timestamp.valueOf(donHang.getNgayHenTra()));
            stmt.setString(4, donHang.getTrangThai());
            stmt.setString(5, donHang.getGhiChu());
            stmt.setString(6, donHang.getNguoiTao());
            
            int result = stmt.executeUpdate();
            if (result > 0) {
                ResultSet rs = stmt.getGeneratedKeys();
                if (rs.next()) {
                    return rs.getInt(1); // Trả về mã đơn hàng vừa tạo
                }
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi thêm đơn hàng: " + e.getMessage());
        }
        
        return -1;
    }
    
    // Cập nhật thông tin đơn hàng
    public boolean capNhatDonHang(DonHang donHang) {
        String sql = "UPDATE DonHang SET MaKH = ?, NgayNhan = ?, NgayHenTra = ?, NgayTra = ?, " +
                    "TrangThai = ?, GhiChu = ? WHERE MaDon = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, donHang.getMaKH());
            stmt.setTimestamp(2, Timestamp.valueOf(donHang.getNgayNhan()));
            stmt.setTimestamp(3, Timestamp.valueOf(donHang.getNgayHenTra()));
            
            if (donHang.getNgayTra() != null) {
                stmt.setTimestamp(4, Timestamp.valueOf(donHang.getNgayTra()));
            } else {
                stmt.setNull(4, Types.TIMESTAMP);
            }
            
            stmt.setString(5, donHang.getTrangThai());
            stmt.setString(6, donHang.getGhiChu());
            stmt.setInt(7, donHang.getMaDon());
            
            return stmt.executeUpdate() > 0;
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi cập nhật đơn hàng: " + e.getMessage());
            return false;
        }
    }
    
    // Cập nhật trạng thái đơn hàng
    public boolean capNhatTrangThai(int maDon, String trangThai) {
        String sql = "UPDATE DonHang SET TrangThai = ?" + 
                    (trangThai.equals("Đã hoàn thành") ? ", NgayTra = GETDATE()" : "") + 
                    " WHERE MaDon = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, trangThai);
            stmt.setInt(2, maDon);
            
            return stmt.executeUpdate() > 0;
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi cập nhật trạng thái đơn hàng: " + e.getMessage());
            return false;
        }
    }
    
    // Lấy đơn hàng theo mã
    public DonHang layDonHangTheoMa(int maDon) {
        String sql = "SELECT dh.*, kh.TenKhachHang, kh.SoDienThoai " +
                    "FROM DonHang dh " +
                    "JOIN KhachHang kh ON dh.MaKH = kh.MaKH " +
                    "WHERE dh.MaDon = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, maDon);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return taoDonHangTuResultSet(rs);
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy đơn hàng theo mã: " + e.getMessage());
        }
        
        return null;
    }
    
    // Lấy danh sách đơn hàng theo khách hàng
    public List<DonHang> layDonHangTheoKhachHang(int maKH) {
        List<DonHang> danhSach = new ArrayList<>();
        String sql = "SELECT dh.*, kh.TenKhachHang, kh.SoDienThoai " +
                    "FROM DonHang dh " +
                    "JOIN KhachHang kh ON dh.MaKH = kh.MaKH " +
                    "WHERE dh.MaKH = ? ORDER BY dh.NgayTao DESC";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, maKH);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                danhSach.add(taoDonHangTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy đơn hàng theo khách hàng: " + e.getMessage());
        }
        
        return danhSach;
    }
    
    // Lấy danh sách tất cả đơn hàng
    public List<DonHang> layTatCaDonHang() {
        List<DonHang> danhSach = new ArrayList<>();
        String sql = "SELECT dh.*, kh.TenKhachHang, kh.SoDienThoai " +
                    "FROM DonHang dh " +
                    "JOIN KhachHang kh ON dh.MaKH = kh.MaKH " +
                    "ORDER BY dh.NgayTao DESC";
        
        try (Connection conn = DBConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                danhSach.add(taoDonHangTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy danh sách đơn hàng: " + e.getMessage());
        }
        
        return danhSach;
    }
    
    // Lấy đơn hàng theo trạng thái
    public List<DonHang> layDonHangTheoTrangThai(String trangThai) {
        List<DonHang> danhSach = new ArrayList<>();
        String sql = "SELECT dh.*, kh.TenKhachHang, kh.SoDienThoai " +
                    "FROM DonHang dh " +
                    "JOIN KhachHang kh ON dh.MaKH = kh.MaKH " +
                    "WHERE dh.TrangThai = ? ORDER BY dh.NgayTao DESC";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, trangThai);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                danhSach.add(taoDonHangTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy đơn hàng theo trạng thái: " + e.getMessage());
        }
        
        return danhSach;
    }
    
    // Tìm kiếm đơn hàng
    public List<DonHang> timKiemDonHang(String maDon, String tenKH, String soDienThoai, String trangThai) {
        List<DonHang> danhSach = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT dh.*, kh.TenKhachHang, kh.SoDienThoai " +
                                            "FROM DonHang dh " +
                                            "JOIN KhachHang kh ON dh.MaKH = kh.MaKH WHERE 1=1");
        
        List<Object> params = new ArrayList<>();
        
        if (maDon != null && !maDon.trim().isEmpty()) {
            sql.append(" AND CAST(dh.MaDon AS VARCHAR) LIKE ?");
            params.add("%" + maDon + "%");
        }
        
        if (tenKH != null && !tenKH.trim().isEmpty()) {
            sql.append(" AND kh.TenKhachHang LIKE ?");
            params.add("%" + tenKH + "%");
        }
        
        if (soDienThoai != null && !soDienThoai.trim().isEmpty()) {
            sql.append(" AND kh.SoDienThoai LIKE ?");
            params.add("%" + soDienThoai + "%");
        }
        
        if (trangThai != null && !trangThai.equals("Tất cả")) {
            sql.append(" AND dh.TrangThai = ?");
            params.add(trangThai);
        }
        
        sql.append(" ORDER BY dh.NgayTao DESC");
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql.toString())) {
            
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }
            
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                danhSach.add(taoDonHangTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi tìm kiếm đơn hàng: " + e.getMessage());
        }
        
        return danhSach;
    }

    // Đếm số đơn hàng theo trạng thái
    public int demDonHangTheoTrangThai(String trangThai) {
        String sql = "SELECT COUNT(*) FROM DonHang WHERE TrangThai = ?";

        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, trangThai);
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                return rs.getInt(1);
            }

        } catch (SQLException e) {
            System.err.println("Lỗi khi đếm đơn hàng theo trạng thái: " + e.getMessage());
        }

        return 0;
    }

    // Lấy tổng doanh thu theo ngày
    public BigDecimal layDoanhThuTheoNgay(LocalDateTime ngay) {
        String sql = "SELECT SUM(TongTien) FROM DonHang WHERE CAST(NgayTao AS DATE) = ? AND TrangThai = 'Đã hoàn thành'";

        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setDate(1, Date.valueOf(ngay.toLocalDate()));
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                BigDecimal result = rs.getBigDecimal(1);
                return result != null ? result : BigDecimal.ZERO;
            }

        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy doanh thu theo ngày: " + e.getMessage());
        }

        return BigDecimal.ZERO;
    }

    // Phương thức helper để tạo đối tượng DonHang từ ResultSet
    private DonHang taoDonHangTuResultSet(ResultSet rs) throws SQLException {
        DonHang dh = new DonHang();
        dh.setMaDon(rs.getInt("MaDon"));
        dh.setMaKH(rs.getInt("MaKH"));

        Timestamp ngayNhan = rs.getTimestamp("NgayNhan");
        if (ngayNhan != null) {
            dh.setNgayNhan(ngayNhan.toLocalDateTime());
        }

        Timestamp ngayTra = rs.getTimestamp("NgayTra");
        if (ngayTra != null) {
            dh.setNgayTra(ngayTra.toLocalDateTime());
        }

        Timestamp ngayHenTra = rs.getTimestamp("NgayHenTra");
        if (ngayHenTra != null) {
            dh.setNgayHenTra(ngayHenTra.toLocalDateTime());
        }

        BigDecimal tongTien = rs.getBigDecimal("TongTien");
        dh.setTongTien(tongTien != null ? tongTien : BigDecimal.ZERO);

        dh.setTrangThai(rs.getString("TrangThai"));
        dh.setGhiChu(rs.getString("GhiChu"));
        dh.setNguoiTao(rs.getString("NguoiTao"));

        Timestamp ngayTao = rs.getTimestamp("NgayTao");
        if (ngayTao != null) {
            dh.setNgayTao(ngayTao.toLocalDateTime());
        }

        // Thông tin khách hàng
        dh.setTenKhachHang(rs.getString("TenKhachHang"));
        dh.setSoDienThoai(rs.getString("SoDienThoai"));

        return dh;
    }
}
