package model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Model class cho Đơn hàng
 * <AUTHOR>
 */
public class DonHang {
    private int maDon;
    private int maKH;
    private LocalDateTime ngayNhan;
    private LocalDateTime ngayTra;
    private LocalDateTime ngayHenTra;
    private BigDecimal tongTien;
    private String trangThai;
    private String ghiChu;
    private String nguoiTao;
    private LocalDateTime ngayTao;
    
    // Danh sách chi tiết đơn hàng
    private List<ChiTietDonHang> chiTietDonHangs;
    
    // Thông tin khách hàng (để hiển thị)
    private String tenKhachHang;
    private String soDienThoai;

    // Constructor mặc định
    public DonHang() {
        this.ngayTao = LocalDateTime.now();
        this.trangThai = "Đang xử lý";
        this.tongTien = BigDecimal.ZERO;
        this.chiTietDonHangs = new ArrayList<>();
    }

    // Constructor đầy đủ
    public DonHang(int maDon, int maKH, LocalDateTime ngayNhan, LocalDateTime ngayHenTra, 
                   String trangThai, String ghiChu, String nguoiTao) {
        this.maDon = maDon;
        this.maKH = maKH;
        this.ngayNhan = ngayNhan;
        this.ngayHenTra = ngayHenTra;
        this.trangThai = trangThai;
        this.ghiChu = ghiChu;
        this.nguoiTao = nguoiTao;
        this.ngayTao = LocalDateTime.now();
        this.tongTien = BigDecimal.ZERO;
        this.chiTietDonHangs = new ArrayList<>();
    }

    // Constructor không có mã (dùng khi thêm mới)
    public DonHang(int maKH, LocalDateTime ngayNhan, LocalDateTime ngayHenTra, 
                   String ghiChu, String nguoiTao) {
        this.maKH = maKH;
        this.ngayNhan = ngayNhan;
        this.ngayHenTra = ngayHenTra;
        this.ghiChu = ghiChu;
        this.nguoiTao = nguoiTao;
        this.ngayTao = LocalDateTime.now();
        this.trangThai = "Đang xử lý";
        this.tongTien = BigDecimal.ZERO;
        this.chiTietDonHangs = new ArrayList<>();
    }

    // Getters và Setters
    public int getMaDon() {
        return maDon;
    }

    public void setMaDon(int maDon) {
        this.maDon = maDon;
    }

    public int getMaKH() {
        return maKH;
    }

    public void setMaKH(int maKH) {
        this.maKH = maKH;
    }

    public LocalDateTime getNgayNhan() {
        return ngayNhan;
    }

    public void setNgayNhan(LocalDateTime ngayNhan) {
        this.ngayNhan = ngayNhan;
    }

    public LocalDateTime getNgayTra() {
        return ngayTra;
    }

    public void setNgayTra(LocalDateTime ngayTra) {
        this.ngayTra = ngayTra;
    }

    public LocalDateTime getNgayHenTra() {
        return ngayHenTra;
    }

    public void setNgayHenTra(LocalDateTime ngayHenTra) {
        this.ngayHenTra = ngayHenTra;
    }

    public BigDecimal getTongTien() {
        return tongTien;
    }

    public void setTongTien(BigDecimal tongTien) {
        this.tongTien = tongTien;
    }

    public String getTrangThai() {
        return trangThai;
    }

    public void setTrangThai(String trangThai) {
        this.trangThai = trangThai;
    }

    public String getGhiChu() {
        return ghiChu;
    }

    public void setGhiChu(String ghiChu) {
        this.ghiChu = ghiChu;
    }

    public String getNguoiTao() {
        return nguoiTao;
    }

    public void setNguoiTao(String nguoiTao) {
        this.nguoiTao = nguoiTao;
    }

    public LocalDateTime getNgayTao() {
        return ngayTao;
    }

    public void setNgayTao(LocalDateTime ngayTao) {
        this.ngayTao = ngayTao;
    }

    public List<ChiTietDonHang> getChiTietDonHangs() {
        return chiTietDonHangs;
    }

    public void setChiTietDonHangs(List<ChiTietDonHang> chiTietDonHangs) {
        this.chiTietDonHangs = chiTietDonHangs;
        tinhTongTien();
    }

    public String getTenKhachHang() {
        return tenKhachHang;
    }

    public void setTenKhachHang(String tenKhachHang) {
        this.tenKhachHang = tenKhachHang;
    }

    public String getSoDienThoai() {
        return soDienThoai;
    }

    public void setSoDienThoai(String soDienThoai) {
        this.soDienThoai = soDienThoai;
    }

    // Phương thức thêm chi tiết đơn hàng
    public void themChiTiet(ChiTietDonHang chiTiet) {
        this.chiTietDonHangs.add(chiTiet);
        tinhTongTien();
    }

    // Phương thức xóa chi tiết đơn hàng
    public void xoaChiTiet(int index) {
        if (index >= 0 && index < chiTietDonHangs.size()) {
            this.chiTietDonHangs.remove(index);
            tinhTongTien();
        }
    }

    // Phương thức tính tổng tiền
    public void tinhTongTien() {
        this.tongTien = chiTietDonHangs.stream()
                .map(ChiTietDonHang::getThanhTien)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    // Kiểm tra đơn hàng đã hoàn thành chưa
    public boolean daHoanThanh() {
        return "Đã hoàn thành".equals(trangThai);
    }

    // Kiểm tra đơn hàng đã hủy chưa
    public boolean daHuy() {
        return "Đã hủy".equals(trangThai);
    }

    @Override
    public String toString() {
        return "DonHang{" +
                "maDon=" + maDon +
                ", maKH=" + maKH +
                ", ngayNhan=" + ngayNhan +
                ", ngayTra=" + ngayTra +
                ", ngayHenTra=" + ngayHenTra +
                ", tongTien=" + tongTien +
                ", trangThai='" + trangThai + '\'' +
                ", ghiChu='" + ghiChu + '\'' +
                '}';
    }
}
