package model;

import java.math.BigDecimal;

/**
 * Model class cho Dịch vụ
 * <AUTHOR>
 */
public class DichVu {
    private int maDV;
    private String tenDichVu;
    private BigDecimal donGia;
    private String donVi;
    private String moTa;
    private boolean trangThai;

    // Constructor mặc định
    public DichVu() {
        this.trangThai = true;
        this.donVi = "kg";
    }

    // Constructor đầy đủ
    public DichVu(int maDV, String tenDichVu, BigDecimal donGia, String donVi, String moTa) {
        this.maDV = maDV;
        this.tenDichVu = tenDichVu;
        this.donGia = donGia;
        this.donVi = donVi;
        this.moTa = moTa;
        this.trangThai = true;
    }

    // Constructor không có mã (dùng khi thêm mới)
    public DichVu(String tenDichVu, BigDecimal donGia, String donVi, String moTa) {
        this.tenDichVu = tenDichVu;
        this.donGia = donGia;
        this.donVi = donVi;
        this.moTa = moTa;
        this.trangThai = true;
    }

    // Constructor với double cho đơn giá (tiện lợi)
    public DichVu(String tenDichVu, double donGia, String donVi, String moTa) {
        this.tenDichVu = tenDichVu;
        this.donGia = BigDecimal.valueOf(donGia);
        this.donVi = donVi;
        this.moTa = moTa;
        this.trangThai = true;
    }

    // Getters và Setters
    public int getMaDV() {
        return maDV;
    }

    public void setMaDV(int maDV) {
        this.maDV = maDV;
    }

    public String getTenDichVu() {
        return tenDichVu;
    }

    public void setTenDichVu(String tenDichVu) {
        this.tenDichVu = tenDichVu;
    }

    public BigDecimal getDonGia() {
        return donGia;
    }

    public void setDonGia(BigDecimal donGia) {
        this.donGia = donGia;
    }

    public void setDonGia(double donGia) {
        this.donGia = BigDecimal.valueOf(donGia);
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public String getMoTa() {
        return moTa;
    }

    public void setMoTa(String moTa) {
        this.moTa = moTa;
    }

    public boolean isTrangThai() {
        return trangThai;
    }

    public void setTrangThai(boolean trangThai) {
        this.trangThai = trangThai;
    }

    // Phương thức tính thành tiền
    public BigDecimal tinhThanhTien(BigDecimal soLuong) {
        return donGia.multiply(soLuong);
    }

    public BigDecimal tinhThanhTien(double soLuong) {
        return donGia.multiply(BigDecimal.valueOf(soLuong));
    }

    @Override
    public String toString() {
        return "DichVu{" +
                "maDV=" + maDV +
                ", tenDichVu='" + tenDichVu + '\'' +
                ", donGia=" + donGia +
                ", donVi='" + donVi + '\'' +
                ", moTa='" + moTa + '\'' +
                ", trangThai=" + trangThai +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        DichVu dichVu = (DichVu) obj;
        return maDV == dichVu.maDV;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(maDV);
    }
}
