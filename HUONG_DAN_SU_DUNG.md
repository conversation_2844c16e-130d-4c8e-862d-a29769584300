# HƯỚNG DẪN SỬ DỤNG ỨNG DỤNG QUẢN LÝ GIẶT ỦI

## 1. CÀI ĐẶT VÀ KHỞI ĐỘNG

### Bước 1: <PERSON><PERSON><PERSON> bị môi trường
- Cài đặt Java JDK 11 trở lên
- Cài đặt SQL Server (LocalDB hoặc SQL Server Express)
- Cài đặt Maven 3.6+

### Bước 2: Thiết lập database
1. Mở Command Prompt vớ<PERSON> quyền Administrator
2. <PERSON>y<PERSON><PERSON> đến thư mục `database`
3. Chạy lệnh: `RunDatabase.bat`
4. Đợi script hoàn thành việc tạo database và dữ liệu mẫu

### Bước 3: Khởi động ứng dụng
1. Mở Command Prompt tại thư mục gốc dự án
2. <PERSON><PERSON><PERSON> lệnh: `run.bat`
3. Hoặc chạy thủ công: `mvn clean compile exec:java`

### Bước 4: Test kết nối (tùy chọn)
- Chạy `test.bat` để kiểm tra kết nối database và các DAO

## 2. GIAO DIỆN CHÍNH

Ứng dụng có giao diện menu với các chức năng:
- **Kh<PERSON>ch hàng** → <PERSON><PERSON><PERSON>n lý khách hàng
- **Đơn hàng** → Quản lý đơn hàng
- **Dịch vụ** → Quản lý dịch vụ
- **Thống kê** → Báo cáo doanh thu và đơn hàng
- **Thanh toán** → Thanh toán hóa đơn
- **Tra cứu** → Tìm kiếm đơn hàng

## 3. HƯỚNG DẪN SỬ DỤNG TỪNG CHỨC NĂNG

### 3.1. QUẢN LÝ KHÁCH HÀNG

**Thêm khách hàng mới:**
1. Nhập đầy đủ thông tin: Họ tên, Số điện thoại, Địa chỉ
2. Click nút "Thêm"
3. Hệ thống sẽ kiểm tra số điện thoại trùng lặp

**Sửa thông tin khách hàng:**
1. Click chọn khách hàng trong bảng
2. Thông tin sẽ hiển thị trong form
3. Chỉnh sửa thông tin cần thiết
4. Click nút "Sửa"

**Xóa khách hàng:**
1. Click chọn khách hàng trong bảng
2. Click nút "Xóa"
3. Xác nhận xóa trong hộp thoại

**Lưu ý:**
- Số điện thoại phải có 10-11 chữ số
- Không được để trống các trường bắt buộc
- Xóa khách hàng là soft delete (không xóa vĩnh viễn)

### 3.2. QUẢN LÝ DỊCH VỤ

**Thêm dịch vụ mới:**
1. Chọn loại dịch vụ từ ComboBox
2. Nhập đơn giá (VNĐ)
3. Click nút "Thêm"

**Sửa dịch vụ:**
1. Click chọn dịch vụ trong bảng
2. Thông tin hiển thị trong form
3. Chỉnh sửa và click "Sửa"

**Xóa dịch vụ:**
1. Chọn dịch vụ cần xóa
2. Click nút "Xóa"
3. Xác nhận trong hộp thoại

**Các loại dịch vụ có sẵn:**
- Giặt ủi thường
- Giặt hấp
- Ủi đồ
- Gấp đồ
- Giặt nhanh
- Giặt khô
- Giặt chăn màn

### 3.3. QUẢN LÝ ĐỠN HÀNG

**Tạo đơn hàng mới:**
1. Nhập thông tin khách hàng
2. Chọn ngày nhận và ngày hẹn trả
3. Chọn dịch vụ và nhập số lượng
4. Hệ thống tự động tính tổng tiền
5. Click "Tạo đơn"

**Cập nhật trạng thái đơn hàng:**
1. Chọn đơn hàng trong bảng
2. Thay đổi trạng thái
3. Click "Cập nhật"

**Các trạng thái đơn hàng:**
- Đang xử lý
- Đã hoàn thành
- Đã hủy

### 3.4. THANH TOÁN HÓA ĐƠN

**Tạo hóa đơn:**
1. Chọn đơn hàng cần thanh toán
2. Nhập số tiền thanh toán
3. Chọn phương thức thanh toán
4. Click "Thanh toán"

**In hóa đơn:**
1. Chọn hóa đơn cần in
2. Click "In hóa đơn"
3. Hệ thống sẽ mở hộp thoại in

**Phương thức thanh toán:**
- Tiền mặt
- Chuyển khoản
- Thẻ

### 3.5. THỐNG KÊ

**Xem báo cáo doanh thu:**
- Hiển thị doanh thu theo ngày
- Tổng số hóa đơn
- Số tiền đã thu và còn lại

**Xem thống kê đơn hàng:**
- Danh sách tất cả đơn hàng
- Phân loại theo trạng thái
- Thông tin chi tiết từng đơn

### 3.6. TRA CỨU ĐƠN HÀNG

**Tìm kiếm đơn hàng:**
1. Nhập thông tin tìm kiếm:
   - Mã đơn hàng
   - Tên khách hàng
   - Số điện thoại
   - Trạng thái
2. Click "Tìm kiếm"
3. Kết quả hiển thị trong bảng

## 4. XỬ LÝ LỖI THƯỜNG GẶP

### Lỗi kết nối database:
**Triệu chứng:** Ứng dụng báo lỗi không kết nối được database
**Giải pháp:**
1. Kiểm tra SQL Server đang chạy
2. Kiểm tra thông tin kết nối trong `DBConnection.java`
3. Chạy lại script `RunDatabase.bat`
4. Kiểm tra firewall và port 1433

### Lỗi Maven:
**Triệu chứng:** Không build được ứng dụng
**Giải pháp:**
1. Chạy `mvn clean install`
2. Kiểm tra kết nối internet
3. Xóa thư mục `.m2/repository` và build lại

### Lỗi Java version:
**Triệu chứng:** Báo lỗi version không tương thích
**Giải pháp:**
1. Kiểm tra Java version: `java -version`
2. Cài đặt Java JDK 11 trở lên
3. Cập nhật JAVA_HOME

### Lỗi giao diện:
**Triệu chứng:** Giao diện hiển thị không đúng
**Giải pháp:**
1. Thay đổi Look and Feel trong `GiatUiAPP.java`
2. Kiểm tra độ phân giải màn hình
3. Restart ứng dụng

## 5. BACKUP VÀ RESTORE DATABASE

### Backup database:
```sql
BACKUP DATABASE QuanLyGiatUi 
TO DISK = 'C:\Backup\QuanLyGiatUi.bak'
```

### Restore database:
```sql
RESTORE DATABASE QuanLyGiatUi 
FROM DISK = 'C:\Backup\QuanLyGiatUi.bak'
```

## 6. LIÊN HỆ HỖ TRỢ

Nếu gặp vấn đề khi sử dụng, vui lòng:
1. Kiểm tra log trong console
2. Chạy test để xác định lỗi
3. Liên hệ nhóm phát triển với thông tin chi tiết lỗi

---
**Phiên bản:** 1.0  
**Ngày cập nhật:** 2024  
**Nhóm phát triển:** Sinh viên Lập trình Java
