/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/GUIForms/JPanel.java to edit this template
 */
package view;

import model.DichVu;
import model.DichVuDAO;
import javax.swing.JOptionPane;
import javax.swing.table.DefaultTableModel;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QuanLyDichVu extends javax.swing.JPanel {

    private DefaultTableModel modelDichVu;
    private DichVuDAO dichVuDAO;

    public QuanLyDichVu() {
        initComponents();
        dichVuDAO = new DichVuDAO();
        setupTable();
        loadComboBoxDichVu();
        loadDanhSachDichVu();
    }

    private void setupTable() {
        modelDichVu = new DefaultTableModel(
            new Object[]{"Mã DV", "Tên <PERSON>", "<PERSON><PERSON><PERSON> (VNĐ)", "Đơ<PERSON>", "<PERSON><PERSON>ả"}, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        tblbangdichvu.setModel(modelDichVu);

        // Thiết lập độ rộng cột
        tblbangdichvu.getColumnModel().getColumn(0).setPreferredWidth(60);  // Mã DV
        tblbangdichvu.getColumnModel().getColumn(1).setPreferredWidth(150); // Tên DV
        tblbangdichvu.getColumnModel().getColumn(2).setPreferredWidth(100); // Đơn giá
        tblbangdichvu.getColumnModel().getColumn(3).setPreferredWidth(80);  // Đơn vị
        tblbangdichvu.getColumnModel().getColumn(4).setPreferredWidth(200); // Mô tả

        // Thêm sự kiện click vào bảng
        tblbangdichvu.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                int selectedRow = tblbangdichvu.getSelectedRow();
                if (selectedRow >= 0) {
                    cbodichvu.setSelectedItem(modelDichVu.getValueAt(selectedRow, 1).toString());
                    txtdongia.setText(modelDichVu.getValueAt(selectedRow, 2).toString());
                }
            }
        });
    }

    private void loadComboBoxDichVu() {
        cbodichvu.removeAllItems();
        cbodichvu.addItem("Giặt ủi thường");
        cbodichvu.addItem("Giặt hấp");
        cbodichvu.addItem("Ủi đồ");
        cbodichvu.addItem("Gấp đồ");
        cbodichvu.addItem("Giặt nhanh");
        cbodichvu.addItem("Giặt khô");
        cbodichvu.addItem("Giặt chăn màn");
    }

    private void loadDanhSachDichVu() {
        try {
            List<DichVu> danhSach = dichVuDAO.layTatCaDichVu();
            modelDichVu.setRowCount(0);

            for (DichVu dv : danhSach) {
                modelDichVu.addRow(new Object[]{
                    dv.getMaDV(),
                    dv.getTenDichVu(),
                    dv.getDonGia(),
                    dv.getDonVi(),
                    dv.getMoTa()
                });
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "Lỗi khi tải danh sách dịch vụ: " + e.getMessage(),
                                        "Lỗi", JOptionPane.ERROR_MESSAGE);
        }
    }

    private void clearForm() {
        cbodichvu.setSelectedIndex(0);
        txtdongia.setText("");
        tblbangdichvu.clearSelection();
    }

    private boolean validateForm() {
        if (cbodichvu.getSelectedItem() == null) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn dịch vụ!");
            return false;
        }

        if (txtdongia.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "Vui lòng nhập đơn giá!");
            txtdongia.requestFocus();
            return false;
        }

        try {
            double donGia = Double.parseDouble(txtdongia.getText().trim());
            if (donGia <= 0) {
                JOptionPane.showMessageDialog(this, "Đơn giá phải lớn hơn 0!");
                txtdongia.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Đơn giá không hợp lệ!");
            txtdongia.requestFocus();
            return false;
        }

        return true;
    }


    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        jScrollPane1 = new javax.swing.JScrollPane();
        jTable1 = new javax.swing.JTable();
        jPanel1 = new javax.swing.JPanel();
        jLabel1 = new javax.swing.JLabel();
        jLabel2 = new javax.swing.JLabel();
        jLabel3 = new javax.swing.JLabel();
        jLabel5 = new javax.swing.JLabel();
        txtmadichvu = new javax.swing.JTextField();
        txtdongia = new javax.swing.JTextField();
        jScrollPane2 = new javax.swing.JScrollPane();
        tblbangdichvu = new javax.swing.JTable();
        jLabel4 = new javax.swing.JLabel();
        jLabel6 = new javax.swing.JLabel();
        cbodichvu = new javax.swing.JComboBox<>();
        btnthem = new javax.swing.JButton();
        btnsua = new javax.swing.JButton();
        btnxoa = new javax.swing.JButton();
        btnlammoi = new javax.swing.JButton();

        jTable1.setModel(new javax.swing.table.DefaultTableModel(
            new Object [][] {
                {null, null, null, null},
                {null, null, null, null},
                {null, null, null, null},
                {null, null, null, null}
            },
            new String [] {
                "Title 1", "Title 2", "Title 3", "Title 4"
            }
        ));
        jScrollPane1.setViewportView(jTable1);

        jLabel1.setFont(new java.awt.Font("Segoe UI", 0, 24)); // NOI18N
        jLabel1.setForeground(new java.awt.Color(0, 0, 255));
        jLabel1.setText("Quản Lý Dịch Vụ");

        javax.swing.GroupLayout jPanel1Layout = new javax.swing.GroupLayout(jPanel1);
        jPanel1.setLayout(jPanel1Layout);
        jPanel1Layout.setHorizontalGroup(
            jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel1Layout.createSequentialGroup()
                .addGap(274, 274, 274)
                .addComponent(jLabel1, javax.swing.GroupLayout.PREFERRED_SIZE, 192, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addContainerGap(javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
        );
        jPanel1Layout.setVerticalGroup(
            jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel1Layout.createSequentialGroup()
                .addContainerGap()
                .addComponent(jLabel1, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .addContainerGap())
        );

        jLabel2.setFont(new java.awt.Font("Segoe UI", 0, 16)); // NOI18N
        jLabel2.setText("Mã Dịch Vụ");

        jLabel3.setFont(new java.awt.Font("Segoe UI", 0, 16)); // NOI18N
        jLabel3.setText("Dịch  Vụ");

        jLabel5.setFont(new java.awt.Font("Segoe UI", 0, 16)); // NOI18N
        jLabel5.setText("Đơn Giá (VNĐ)");

        txtmadichvu.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtmadichvuActionPerformed(evt);
            }
        });

        tblbangdichvu.setModel(new javax.swing.table.DefaultTableModel(
            new Object [][] {
                {null, null, null},
                {null, null, null},
                {null, null, null},
                {null, null, null}
            },
            new String [] {
                "Mã Dịch Vụ", "Tên Dịch Vụ", "Đơn Giá (VNĐ)"
            }
        ));
        jScrollPane2.setViewportView(tblbangdichvu);

        jLabel4.setFont(new java.awt.Font("Segoe UI", 0, 16)); // NOI18N
        jLabel4.setText("Bảng Dịch Vụ");

        jLabel6.setFont(new java.awt.Font("Segoe UI", 0, 16)); // NOI18N
        jLabel6.setText("Mô Tả Đơn Hàng");

        cbodichvu.setModel(new javax.swing.DefaultComboBoxModel<>(new String[] { "Item 1", "Item 2", "Item 3", "Item 4" }));

        btnthem.setFont(new java.awt.Font("Segoe UI", 0, 16)); // NOI18N
        btnthem.setText("Thêm ");
        btnthem.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnthemActionPerformed(evt);
            }
        });

        btnsua.setFont(new java.awt.Font("Segoe UI", 0, 16)); // NOI18N
        btnsua.setText("Sửa");
        btnsua.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnsuaActionPerformed(evt);
            }
        });

        btnxoa.setFont(new java.awt.Font("Segoe UI", 0, 16)); // NOI18N
        btnxoa.setText("Xóa");
        btnxoa.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnxoaActionPerformed(evt);
            }
        });

        btnlammoi.setFont(new java.awt.Font("Segoe UI", 0, 16)); // NOI18N
        btnlammoi.setText("Làm Mới");
        btnlammoi.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnlammoiActionPerformed(evt);
            }
        });

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(this);
        this.setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addComponent(jPanel1, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
            .addGroup(layout.createSequentialGroup()
                .addGap(48, 48, 48)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING, false)
                        .addComponent(jScrollPane2, javax.swing.GroupLayout.DEFAULT_SIZE, 737, Short.MAX_VALUE)
                        .addGroup(layout.createSequentialGroup()
                            .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                .addComponent(jLabel5)
                                .addComponent(jLabel3)
                                .addComponent(jLabel2)
                                .addComponent(jLabel4, javax.swing.GroupLayout.PREFERRED_SIZE, 133, javax.swing.GroupLayout.PREFERRED_SIZE))
                            .addGap(40, 40, 40)
                            .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING, false)
                                .addGroup(layout.createSequentialGroup()
                                    .addComponent(btnthem)
                                    .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                                    .addComponent(btnsua)
                                    .addGap(68, 68, 68)
                                    .addComponent(btnxoa)
                                    .addGap(58, 58, 58)
                                    .addComponent(btnlammoi))
                                .addComponent(txtmadichvu, javax.swing.GroupLayout.DEFAULT_SIZE, 486, Short.MAX_VALUE)
                                .addComponent(txtdongia, javax.swing.GroupLayout.DEFAULT_SIZE, 486, Short.MAX_VALUE)
                                .addComponent(cbodichvu, 0, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
                            .addGap(29, 29, 29)))
                    .addComponent(jLabel6, javax.swing.GroupLayout.PREFERRED_SIZE, 171, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addContainerGap(50, Short.MAX_VALUE))
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addComponent(jPanel1, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(43, 43, 43)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(txtmadichvu, javax.swing.GroupLayout.PREFERRED_SIZE, 30, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(jLabel2))
                .addGap(35, 35, 35)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel3)
                    .addComponent(cbodichvu, javax.swing.GroupLayout.PREFERRED_SIZE, 30, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(31, 31, 31)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.TRAILING)
                    .addComponent(jLabel5)
                    .addComponent(txtdongia, javax.swing.GroupLayout.PREFERRED_SIZE, 30, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createSequentialGroup()
                        .addGap(58, 58, 58)
                        .addComponent(jLabel4))
                    .addGroup(layout.createSequentialGroup()
                        .addGap(36, 36, 36)
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                            .addComponent(btnthem, javax.swing.GroupLayout.PREFERRED_SIZE, 30, javax.swing.GroupLayout.PREFERRED_SIZE)
                            .addComponent(btnlammoi, javax.swing.GroupLayout.PREFERRED_SIZE, 30, javax.swing.GroupLayout.PREFERRED_SIZE)
                            .addComponent(btnxoa, javax.swing.GroupLayout.PREFERRED_SIZE, 30, javax.swing.GroupLayout.PREFERRED_SIZE)
                            .addComponent(btnsua, javax.swing.GroupLayout.PREFERRED_SIZE, 30, javax.swing.GroupLayout.PREFERRED_SIZE))))
                .addGap(18, 18, 18)
                .addComponent(jScrollPane2, javax.swing.GroupLayout.PREFERRED_SIZE, 176, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(31, 31, 31)
                .addComponent(jLabel6)
                .addContainerGap(111, Short.MAX_VALUE))
        );
    }// </editor-fold>//GEN-END:initComponents

    private void txtmadichvuActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtmadichvuActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtmadichvuActionPerformed

    private void btnsuaActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_btnsuaActionPerformed
          DefaultTableModel modelDichVu = (DefaultTableModel) tblbangdichvu.getModel();

    int selectedRow = tblbangdichvu.getSelectedRow();
    if (selectedRow == -1) {
        JOptionPane.showMessageDialog(this, "Vui lòng chọn dịch vụ cần sửa!");
        return;
    }

    String maDV = txtmadichvu.getText().trim();
    String tenDV = cbodichvu.getSelectedItem().toString();
    String donGiaStr = txtdongia.getText().trim();

    if (maDV.isEmpty() || tenDV.isEmpty() || donGiaStr.isEmpty()) {
        JOptionPane.showMessageDialog(this, "Vui lòng nhập đầy đủ thông tin dịch vụ!");
        return;
    }

    double donGia;
    try {
        donGia = Double.parseDouble(donGiaStr);
    } catch (NumberFormatException ex) {
        JOptionPane.showMessageDialog(this, "Đơn giá phải là số hợp lệ!");
        return;
    }

    modelDichVu.setValueAt(maDV, selectedRow, 0);
    modelDichVu.setValueAt(tenDV, selectedRow, 1);
    modelDichVu.setValueAt(donGia, selectedRow, 2);

    JOptionPane.showMessageDialog(this, "Đã cập nhật dịch vụ!");
    clearForm();
    }//GEN-LAST:event_btnsuaActionPerformed

    private void btnthemActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_btnthemActionPerformed
        if (!validateForm()) {
            return;
        }

        String tenDV = cbodichvu.getSelectedItem().toString();
        double donGia = Double.parseDouble(txtdongia.getText().trim());
        String donVi = "kg"; // Mặc định
        String moTa = ""; // Có thể thêm field mô tả sau

        // Kiểm tra tên dịch vụ trùng
        if (dichVuDAO.kiemTraTenDichVuTonTai(tenDV, 0)) {
            JOptionPane.showMessageDialog(this, "Tên dịch vụ đã tồn tại!");
            return;
        }

        try {
            DichVu dv = new DichVu(tenDV, donGia, donVi, moTa);

            if (dichVuDAO.themDichVu(dv)) {
                JOptionPane.showMessageDialog(this, "Thêm dịch vụ thành công!");
                loadDanhSachDichVu();
                clearForm();
            } else {
                JOptionPane.showMessageDialog(this, "Không thể thêm dịch vụ!",
                                            "Lỗi", JOptionPane.ERROR_MESSAGE);
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "Lỗi khi thêm dịch vụ: " + e.getMessage(),
                                        "Lỗi", JOptionPane.ERROR_MESSAGE);
        }
    }//GEN-LAST:event_btnthemActionPerformed

    private void btnxoaActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_btnxoaActionPerformed
        int selectedRow = tblbangdichvu.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "Vui lòng chọn dịch vụ cần xóa!");
            return;
        }

        int maDV = (Integer) modelDichVu.getValueAt(selectedRow, 0);
        String tenDV = (String) modelDichVu.getValueAt(selectedRow, 1);

        int confirm = JOptionPane.showConfirmDialog(this,
            "Bạn có chắc muốn xóa dịch vụ: " + tenDV + "?",
            "Xác nhận xóa",
            JOptionPane.YES_NO_OPTION);

        if (confirm == JOptionPane.YES_OPTION) {
            try {
                if (dichVuDAO.xoaDichVu(maDV)) {
                    JOptionPane.showMessageDialog(this, "Xóa dịch vụ thành công!");
                    loadDanhSachDichVu();
                    clearForm();
                } else {
                    JOptionPane.showMessageDialog(this, "Không thể xóa dịch vụ!",
                                                "Lỗi", JOptionPane.ERROR_MESSAGE);
                }
            } catch (Exception e) {
                JOptionPane.showMessageDialog(this, "Lỗi khi xóa dịch vụ: " + e.getMessage(),
                                            "Lỗi", JOptionPane.ERROR_MESSAGE);
            }
        }
    }//GEN-LAST:event_btnxoaActionPerformed
    private void btnlammoiActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_btnlammoiActionPerformed
       clearForm();
    }//GEN-LAST:event_btnlammoiActionPerformed


    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton btnlammoi;
    private javax.swing.JButton btnsua;
    private javax.swing.JButton btnthem;
    private javax.swing.JButton btnxoa;
    private javax.swing.JComboBox<String> cbodichvu;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel2;
    private javax.swing.JLabel jLabel3;
    private javax.swing.JLabel jLabel4;
    private javax.swing.JLabel jLabel5;
    private javax.swing.JLabel jLabel6;
    private javax.swing.JPanel jPanel1;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JScrollPane jScrollPane2;
    private javax.swing.JTable jTable1;
    private javax.swing.JTable tblbangdichvu;
    private javax.swing.JTextField txtdongia;
    private javax.swing.JTextField txtmadichvu;
    // End of variables declaration//GEN-END:variables

    private void clearForm() {
        cbodichvu.setSelectedIndex(0);
        txtdongia.setText("");
        tblbangdichvu.clearSelection();
    }
}
