package model;

import java.time.LocalDateTime;

/**
 * Model class cho Khách hàng
 * <AUTHOR>
 */
public class KhachHang {
    private int maKH;
    private String tenKhachHang;
    private String soDienThoai;
    private String diaChi;
    private String email;
    private LocalDateTime ngayTao;
    private boolean trangThai;

    // Constructor mặc định
    public KhachHang() {
        this.ngayTao = LocalDateTime.now();
        this.trangThai = true;
    }

    // Constructor đầy đủ
    public KhachHang(int maKH, String tenKhachHang, String soDienThoai, String diaChi, String email) {
        this.maKH = maKH;
        this.tenKhachHang = tenKhachHang;
        this.soDienThoai = soDienThoai;
        this.diaChi = diaChi;
        this.email = email;
        this.ngayTao = LocalDateTime.now();
        this.trangThai = true;
    }

    // Constructor không có mã (dùng khi thêm mới)
    public KhachHang(String tenKhachHang, String soDienThoai, String diaChi, String email) {
        this.tenKhachHang = tenKhachHang;
        this.soDienThoai = soDienThoai;
        this.diaChi = diaChi;
        this.email = email;
        this.ngayTao = LocalDateTime.now();
        this.trangThai = true;
    }

    // Getters và Setters
    public int getMaKH() {
        return maKH;
    }

    public void setMaKH(int maKH) {
        this.maKH = maKH;
    }

    public String getTenKhachHang() {
        return tenKhachHang;
    }

    public void setTenKhachHang(String tenKhachHang) {
        this.tenKhachHang = tenKhachHang;
    }

    public String getSoDienThoai() {
        return soDienThoai;
    }

    public void setSoDienThoai(String soDienThoai) {
        this.soDienThoai = soDienThoai;
    }

    public String getDiaChi() {
        return diaChi;
    }

    public void setDiaChi(String diaChi) {
        this.diaChi = diaChi;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public LocalDateTime getNgayTao() {
        return ngayTao;
    }

    public void setNgayTao(LocalDateTime ngayTao) {
        this.ngayTao = ngayTao;
    }

    public boolean isTrangThai() {
        return trangThai;
    }

    public void setTrangThai(boolean trangThai) {
        this.trangThai = trangThai;
    }

    @Override
    public String toString() {
        return "KhachHang{" +
                "maKH=" + maKH +
                ", tenKhachHang='" + tenKhachHang + '\'' +
                ", soDienThoai='" + soDienThoai + '\'' +
                ", diaChi='" + diaChi + '\'' +
                ", email='" + email + '\'' +
                ", trangThai=" + trangThai +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        KhachHang khachHang = (KhachHang) obj;
        return maKH == khachHang.maKH;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(maKH);
    }
}
