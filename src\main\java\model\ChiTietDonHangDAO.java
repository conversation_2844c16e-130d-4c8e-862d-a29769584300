package model;

import util.DBConnection;
import java.sql.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * DAO class cho Chi tiết đơn hàng
 * <AUTHOR>
 */
public class ChiTietDonHangDAO {
    
    // Thêm chi tiết đơn hàng
    public boolean themChiTietDonHang(ChiTietDonHang chiTiet) {
        String sql = "INSERT INTO ChiTietDonHang (MaDon, MaDV, SoLuong, DonGia, ThanhTien, GhiChu) " +
                    "VALUES (?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, chiTiet.getMaDon());
            stmt.setInt(2, chiTiet.getMaDV());
            stmt.setBigDecimal(3, chiTiet.getSoLuong());
            stmt.setBigDecimal(4, chiTiet.getDonGia());
            stmt.setBigDecimal(5, chiTiet.getThanhTien());
            stmt.setString(6, chiTiet.getGhiChu());
            
            return stmt.executeUpdate() > 0;
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi thêm chi tiết đơn hàng: " + e.getMessage());
            return false;
        }
    }
    
    // Thêm danh sách chi tiết đơn hàng
    public boolean themDanhSachChiTiet(List<ChiTietDonHang> danhSachChiTiet) {
        String sql = "INSERT INTO ChiTietDonHang (MaDon, MaDV, SoLuong, DonGia, ThanhTien, GhiChu) " +
                    "VALUES (?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            conn.setAutoCommit(false); // Bắt đầu transaction
            
            for (ChiTietDonHang chiTiet : danhSachChiTiet) {
                stmt.setInt(1, chiTiet.getMaDon());
                stmt.setInt(2, chiTiet.getMaDV());
                stmt.setBigDecimal(3, chiTiet.getSoLuong());
                stmt.setBigDecimal(4, chiTiet.getDonGia());
                stmt.setBigDecimal(5, chiTiet.getThanhTien());
                stmt.setString(6, chiTiet.getGhiChu());
                stmt.addBatch();
            }
            
            int[] results = stmt.executeBatch();
            conn.commit(); // Commit transaction
            
            // Kiểm tra tất cả đều thành công
            for (int result : results) {
                if (result <= 0) {
                    return false;
                }
            }
            
            return true;
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi thêm danh sách chi tiết đơn hàng: " + e.getMessage());
            return false;
        }
    }
    
    // Cập nhật chi tiết đơn hàng
    public boolean capNhatChiTietDonHang(ChiTietDonHang chiTiet) {
        String sql = "UPDATE ChiTietDonHang SET MaDV = ?, SoLuong = ?, DonGia = ?, ThanhTien = ?, GhiChu = ? " +
                    "WHERE MaChiTiet = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, chiTiet.getMaDV());
            stmt.setBigDecimal(2, chiTiet.getSoLuong());
            stmt.setBigDecimal(3, chiTiet.getDonGia());
            stmt.setBigDecimal(4, chiTiet.getThanhTien());
            stmt.setString(5, chiTiet.getGhiChu());
            stmt.setInt(6, chiTiet.getMaChiTiet());
            
            return stmt.executeUpdate() > 0;
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi cập nhật chi tiết đơn hàng: " + e.getMessage());
            return false;
        }
    }
    
    // Xóa chi tiết đơn hàng
    public boolean xoaChiTietDonHang(int maChiTiet) {
        String sql = "DELETE FROM ChiTietDonHang WHERE MaChiTiet = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, maChiTiet);
            return stmt.executeUpdate() > 0;
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi xóa chi tiết đơn hàng: " + e.getMessage());
            return false;
        }
    }
    
    // Xóa tất cả chi tiết của một đơn hàng
    public boolean xoaTatCaChiTietDonHang(int maDon) {
        String sql = "DELETE FROM ChiTietDonHang WHERE MaDon = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, maDon);
            return stmt.executeUpdate() >= 0; // >= 0 vì có thể không có chi tiết nào
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi xóa tất cả chi tiết đơn hàng: " + e.getMessage());
            return false;
        }
    }
    
    // Lấy chi tiết đơn hàng theo mã
    public ChiTietDonHang layChiTietTheoMa(int maChiTiet) {
        String sql = "SELECT ct.*, dv.TenDichVu, dv.DonVi " +
                    "FROM ChiTietDonHang ct " +
                    "JOIN DichVu dv ON ct.MaDV = dv.MaDV " +
                    "WHERE ct.MaChiTiet = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, maChiTiet);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return taoChiTietTuResultSet(rs);
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy chi tiết theo mã: " + e.getMessage());
        }
        
        return null;
    }
    
    // Lấy danh sách chi tiết theo đơn hàng
    public List<ChiTietDonHang> layChiTietTheoDonHang(int maDon) {
        List<ChiTietDonHang> danhSach = new ArrayList<>();
        String sql = "SELECT ct.*, dv.TenDichVu, dv.DonVi " +
                    "FROM ChiTietDonHang ct " +
                    "JOIN DichVu dv ON ct.MaDV = dv.MaDV " +
                    "WHERE ct.MaDon = ? ORDER BY ct.MaChiTiet";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, maDon);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                danhSach.add(taoChiTietTuResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi lấy chi tiết theo đơn hàng: " + e.getMessage());
        }
        
        return danhSach;
    }
    
    // Tính tổng tiền của một đơn hàng
    public BigDecimal tinhTongTienDonHang(int maDon) {
        String sql = "SELECT SUM(ThanhTien) FROM ChiTietDonHang WHERE MaDon = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, maDon);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                BigDecimal result = rs.getBigDecimal(1);
                return result != null ? result : BigDecimal.ZERO;
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi tính tổng tiền đơn hàng: " + e.getMessage());
        }
        
        return BigDecimal.ZERO;
    }
    
    // Đếm số lượng chi tiết của một đơn hàng
    public int demSoLuongChiTiet(int maDon) {
        String sql = "SELECT COUNT(*) FROM ChiTietDonHang WHERE MaDon = ?";
        
        try (Connection conn = DBConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, maDon);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1);
            }
            
        } catch (SQLException e) {
            System.err.println("Lỗi khi đếm số lượng chi tiết: " + e.getMessage());
        }
        
        return 0;
    }
    
    // Phương thức helper để tạo đối tượng ChiTietDonHang từ ResultSet
    private ChiTietDonHang taoChiTietTuResultSet(ResultSet rs) throws SQLException {
        ChiTietDonHang ct = new ChiTietDonHang();
        ct.setMaChiTiet(rs.getInt("MaChiTiet"));
        ct.setMaDon(rs.getInt("MaDon"));
        ct.setMaDV(rs.getInt("MaDV"));
        ct.setSoLuong(rs.getBigDecimal("SoLuong"));
        ct.setDonGia(rs.getBigDecimal("DonGia"));
        ct.setThanhTien(rs.getBigDecimal("ThanhTien"));
        ct.setGhiChu(rs.getString("GhiChu"));
        
        // Thông tin dịch vụ
        ct.setTenDichVu(rs.getString("TenDichVu"));
        ct.setDonVi(rs.getString("DonVi"));
        
        return ct;
    }
}
