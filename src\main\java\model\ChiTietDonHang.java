package model;

import java.math.BigDecimal;

/**
 * Model class cho Chi tiết đơn hàng
 * <AUTHOR>
 */
public class ChiTietDonHang {
    private int maChiTiet;
    private int maDon;
    private int maDV;
    private BigDecimal soLuong;
    private BigDecimal donGia;
    private BigDecimal thanhTien;
    private String ghiChu;
    
    // Thông tin dịch vụ (để hiển thị)
    private String tenDichVu;
    private String donVi;

    // Constructor mặc định
    public ChiTietDonHang() {
        this.soLuong = BigDecimal.ZERO;
        this.donGia = BigDecimal.ZERO;
        this.thanhTien = BigDecimal.ZERO;
    }

    // Constructor đầy đủ
    public ChiTietDonHang(int maChiTiet, int maDon, int maDV, BigDecimal soLuong, 
                         BigDecimal donGia, String ghiChu) {
        this.maChiTiet = maChiTiet;
        this.maDon = maDon;
        this.maDV = maDV;
        this.soLuong = soLuong;
        this.donGia = donGia;
        this.ghiChu = ghiChu;
        tinhThanhTien();
    }

    // Constructor không có mã chi tiết (dùng khi thêm mới)
    public ChiTietDonHang(int maDon, int maDV, BigDecimal soLuong, BigDecimal donGia, String ghiChu) {
        this.maDon = maDon;
        this.maDV = maDV;
        this.soLuong = soLuong;
        this.donGia = donGia;
        this.ghiChu = ghiChu;
        tinhThanhTien();
    }

    // Constructor với double (tiện lợi)
    public ChiTietDonHang(int maDon, int maDV, double soLuong, double donGia, String ghiChu) {
        this.maDon = maDon;
        this.maDV = maDV;
        this.soLuong = BigDecimal.valueOf(soLuong);
        this.donGia = BigDecimal.valueOf(donGia);
        this.ghiChu = ghiChu;
        tinhThanhTien();
    }

    // Getters và Setters
    public int getMaChiTiet() {
        return maChiTiet;
    }

    public void setMaChiTiet(int maChiTiet) {
        this.maChiTiet = maChiTiet;
    }

    public int getMaDon() {
        return maDon;
    }

    public void setMaDon(int maDon) {
        this.maDon = maDon;
    }

    public int getMaDV() {
        return maDV;
    }

    public void setMaDV(int maDV) {
        this.maDV = maDV;
    }

    public BigDecimal getSoLuong() {
        return soLuong;
    }

    public void setSoLuong(BigDecimal soLuong) {
        this.soLuong = soLuong;
        tinhThanhTien();
    }

    public void setSoLuong(double soLuong) {
        this.soLuong = BigDecimal.valueOf(soLuong);
        tinhThanhTien();
    }

    public BigDecimal getDonGia() {
        return donGia;
    }

    public void setDonGia(BigDecimal donGia) {
        this.donGia = donGia;
        tinhThanhTien();
    }

    public void setDonGia(double donGia) {
        this.donGia = BigDecimal.valueOf(donGia);
        tinhThanhTien();
    }

    public BigDecimal getThanhTien() {
        return thanhTien;
    }

    public void setThanhTien(BigDecimal thanhTien) {
        this.thanhTien = thanhTien;
    }

    public String getGhiChu() {
        return ghiChu;
    }

    public void setGhiChu(String ghiChu) {
        this.ghiChu = ghiChu;
    }

    public String getTenDichVu() {
        return tenDichVu;
    }

    public void setTenDichVu(String tenDichVu) {
        this.tenDichVu = tenDichVu;
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    // Phương thức tính thành tiền
    public void tinhThanhTien() {
        if (soLuong != null && donGia != null) {
            this.thanhTien = soLuong.multiply(donGia);
        } else {
            this.thanhTien = BigDecimal.ZERO;
        }
    }

    @Override
    public String toString() {
        return "ChiTietDonHang{" +
                "maChiTiet=" + maChiTiet +
                ", maDon=" + maDon +
                ", maDV=" + maDV +
                ", soLuong=" + soLuong +
                ", donGia=" + donGia +
                ", thanhTien=" + thanhTien +
                ", ghiChu='" + ghiChu + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ChiTietDonHang that = (ChiTietDonHang) obj;
        return maChiTiet == that.maChiTiet;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(maChiTiet);
    }
}
